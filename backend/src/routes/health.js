/**
 * Health check routes
 */
const express = require('express');
const axios = require('axios');
const { testConnection } = require('../config/database');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * Basic health check
 */
router.get('/', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'lyzr-support-chat-backend',
    version: '1.0.0'
  });
});

/**
 * Detailed health check
 */
router.get('/detailed', async (req, res) => {
  const healthStatus = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'lyzr-support-chat-backend',
    version: '1.0.0',
    checks: {}
  };

  // Check database connection
  try {
    const dbHealthy = await testConnection();
    healthStatus.checks.database = {
      status: dbHealthy ? 'healthy' : 'unhealthy'
    };
    if (!dbHealthy) {
      healthStatus.status = 'unhealthy';
    }
  } catch (error) {
    healthStatus.checks.database = {
      status: 'unhealthy',
      error: error.message
    };
    healthStatus.status = 'unhealthy';
  }

  // Check Agent API connection
  try {
    const agentApiUrl = process.env.AGENT_API_URL;
    if (agentApiUrl) {
      const response = await axios.get(`${agentApiUrl}/health`, {
        timeout: 5000,
        headers: {
          'Authorization': `Bearer ${process.env.AGENT_API_KEY}`
        }
      });
      
      healthStatus.checks.agent_api = {
        status: response.status === 200 ? 'healthy' : 'unhealthy'
      };
      
      if (response.status !== 200) {
        healthStatus.status = 'unhealthy';
      }
    } else {
      healthStatus.checks.agent_api = {
        status: 'not_configured'
      };
    }
  } catch (error) {
    healthStatus.checks.agent_api = {
      status: 'unhealthy',
      error: error.message
    };
    healthStatus.status = 'unhealthy';
  }

  // Check memory usage
  const memUsage = process.memoryUsage();
  healthStatus.checks.memory = {
    status: 'healthy',
    usage: {
      rss: `${Math.round(memUsage.rss / 1024 / 1024)}MB`,
      heapTotal: `${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`,
      heapUsed: `${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`,
      external: `${Math.round(memUsage.external / 1024 / 1024)}MB`
    }
  };

  // Check uptime
  healthStatus.checks.uptime = {
    status: 'healthy',
    uptime: `${Math.round(process.uptime())}s`
  };

  const statusCode = healthStatus.status === 'healthy' ? 200 : 503;
  res.status(statusCode).json(healthStatus);
});

/**
 * Readiness check (for Kubernetes)
 */
router.get('/ready', async (req, res) => {
  try {
    const dbHealthy = await testConnection();
    if (dbHealthy) {
      res.json({ status: 'ready' });
    } else {
      res.status(503).json({ status: 'not_ready', reason: 'database_unavailable' });
    }
  } catch (error) {
    logger.error('Readiness check failed:', error);
    res.status(503).json({ status: 'not_ready', reason: error.message });
  }
});

/**
 * Liveness check (for Kubernetes)
 */
router.get('/live', (req, res) => {
  res.json({ status: 'alive' });
});

module.exports = router;
