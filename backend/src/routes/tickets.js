/**
 * Ticket management routes
 */
const express = require('express');
const { supabase } = require('../config/database');
const { requireAuth, loadUser, requireOrganizationMember } = require('../middleware/auth');
const { validateCreateTicket, validateUpdateTicket, validateUUIDParam, validatePagination } = require('../middleware/validation');
const logger = require('../utils/logger');

const router = express.Router();

// Apply authentication to all routes
router.use(requireAuth);
router.use(loadUser);

/**
 * Get tickets for organization
 */
router.get('/', validatePagination, async (req, res) => {
  try {
    const { organization_id, status, priority, assigned_to, page = 1, limit = 20 } = req.query;
    
    if (!organization_id) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // Check organization membership
    const { data: membership } = await supabase
      .from('organization_members')
      .select('role')
      .eq('organization_id', organization_id)
      .eq('user_id', req.user.id)
      .single();

    if (!membership) {
      return res.status(403).json({ error: 'Access denied to this organization' });
    }

    const offset = (page - 1) * limit;
    let query = supabase
      .from('tickets')
      .select(`
        *,
        chat_sessions(
          id,
          visitor_id,
          visitor_info
        ),
        assigned_user:users!tickets_assigned_to_fkey(
          id,
          first_name,
          last_name,
          email
        )
      `, { count: 'exact' })
      .eq('organization_id', organization_id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (status) query = query.eq('status', status);
    if (priority) query = query.eq('priority', priority);
    if (assigned_to) query = query.eq('assigned_to', assigned_to);

    const { data: tickets, error, count } = await query;

    if (error) {
      logger.error('Error fetching tickets:', error);
      return res.status(500).json({ error: 'Failed to fetch tickets' });
    }

    res.json({
      tickets: tickets || [],
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count || 0,
        pages: Math.ceil((count || 0) / limit)
      }
    });
  } catch (error) {
    logger.error('Error in GET /tickets:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Create new ticket
 */
router.post('/', validateCreateTicket, async (req, res) => {
  try {
    const { title, description, priority = 'medium', session_id, organization_id } = req.body;

    // Check organization membership
    const { data: membership } = await supabase
      .from('organization_members')
      .select('role')
      .eq('organization_id', organization_id)
      .eq('user_id', req.user.id)
      .single();

    if (!membership) {
      return res.status(403).json({ error: 'Access denied to this organization' });
    }

    // Generate ticket number
    const { data: lastTicket } = await supabase
      .from('tickets')
      .select('ticket_number')
      .eq('organization_id', organization_id)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    let ticketNumber = 1;
    if (lastTicket) {
      const lastNumber = parseInt(lastTicket.ticket_number.split('-')[1]) || 0;
      ticketNumber = lastNumber + 1;
    }

    const formattedTicketNumber = `TICKET-${ticketNumber.toString().padStart(4, '0')}`;

    // Create ticket
    const { data: ticket, error } = await supabase
      .from('tickets')
      .insert({
        ticket_number: formattedTicketNumber,
        title,
        description,
        priority,
        status: 'open',
        organization_id,
        session_id,
        created_by: req.user.id
      })
      .select(`
        *,
        chat_sessions(
          id,
          visitor_id,
          visitor_info
        )
      `)
      .single();

    if (error) {
      logger.error('Error creating ticket:', error);
      return res.status(500).json({ error: 'Failed to create ticket' });
    }

    logger.info(`Ticket created: ${ticket.id} by user ${req.user.id}`);
    res.status(201).json(ticket);
  } catch (error) {
    logger.error('Error in POST /tickets:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Get specific ticket
 */
router.get('/:ticketId', validateUUIDParam('ticketId'), async (req, res) => {
  try {
    const { ticketId } = req.params;

    const { data: ticket, error } = await supabase
      .from('tickets')
      .select(`
        *,
        chat_sessions(
          id,
          visitor_id,
          visitor_info,
          messages(
            id,
            type,
            content,
            created_at
          )
        ),
        assigned_user:users!tickets_assigned_to_fkey(
          id,
          first_name,
          last_name,
          email
        ),
        ticket_comments(
          id,
          content,
          author_id,
          author_name,
          is_internal,
          created_at
        ),
        organizations!inner(
          id,
          name,
          organization_members!inner(
            user_id,
            role
          )
        )
      `)
      .eq('id', ticketId)
      .eq('organizations.organization_members.user_id', req.user.id)
      .single();

    if (error || !ticket) {
      return res.status(404).json({ error: 'Ticket not found' });
    }

    res.json(ticket);
  } catch (error) {
    logger.error('Error in GET /tickets/:id:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Update ticket
 */
router.put('/:ticketId', 
  validateUUIDParam('ticketId'),
  validateUpdateTicket,
  async (req, res) => {
    try {
      const { ticketId } = req.params;
      const updates = req.body;

      // Get ticket and check permissions
      const { data: ticket, error: fetchError } = await supabase
        .from('tickets')
        .select(`
          *,
          organizations!inner(
            id,
            organization_members!inner(
              user_id,
              role
            )
          )
        `)
        .eq('id', ticketId)
        .eq('organizations.organization_members.user_id', req.user.id)
        .single();

      if (fetchError || !ticket) {
        return res.status(404).json({ error: 'Ticket not found or access denied' });
      }

      // Update ticket
      const { data: updatedTicket, error } = await supabase
        .from('tickets')
        .update(updates)
        .eq('id', ticketId)
        .select(`
          *,
          assigned_user:users!tickets_assigned_to_fkey(
            id,
            first_name,
            last_name,
            email
          )
        `)
        .single();

      if (error) {
        logger.error('Error updating ticket:', error);
        return res.status(500).json({ error: 'Failed to update ticket' });
      }

      logger.info(`Ticket updated: ${ticketId} by user ${req.user.id}`);
      res.json(updatedTicket);
    } catch (error) {
      logger.error('Error in PUT /tickets/:id:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

/**
 * Add comment to ticket
 */
router.post('/:ticketId/comments', validateUUIDParam('ticketId'), async (req, res) => {
  try {
    const { ticketId } = req.params;
    const { content, is_internal = false } = req.body;

    if (!content || content.trim().length === 0) {
      return res.status(400).json({ error: 'Comment content is required' });
    }

    // Check ticket access
    const { data: ticket, error: ticketError } = await supabase
      .from('tickets')
      .select(`
        id,
        organizations!inner(
          id,
          organization_members!inner(
            user_id,
            role
          )
        )
      `)
      .eq('id', ticketId)
      .eq('organizations.organization_members.user_id', req.user.id)
      .single();

    if (ticketError || !ticket) {
      return res.status(404).json({ error: 'Ticket not found or access denied' });
    }

    // Add comment
    const { data: comment, error } = await supabase
      .from('ticket_comments')
      .insert({
        ticket_id: ticketId,
        content: content.trim(),
        author_id: req.user.id,
        author_name: `${req.user.first_name} ${req.user.last_name}`,
        is_internal
      })
      .select()
      .single();

    if (error) {
      logger.error('Error adding ticket comment:', error);
      return res.status(500).json({ error: 'Failed to add comment' });
    }

    logger.info(`Comment added to ticket ${ticketId} by user ${req.user.id}`);
    res.status(201).json(comment);
  } catch (error) {
    logger.error('Error in POST /tickets/:id/comments:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
