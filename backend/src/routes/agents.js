/**
 * Agent management routes
 */
const express = require('express');
const axios = require('axios');
const { supabase } = require('../config/database');
const { requireAuth, loadUser, requireOrganizationMember, requireOrganizationAdmin } = require('../middleware/auth');
const { validateCreateAgent, validateUpdateAgent, validateUUIDParam } = require('../middleware/validation');
const logger = require('../utils/logger');

const router = express.Router();

// Apply authentication to all routes
router.use(requireAuth);
router.use(loadUser);

/**
 * Get agents for organization
 */
router.get('/', async (req, res) => {
  try {
    const { organization_id } = req.query;
    
    if (!organization_id) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // Check organization membership
    const { data: membership } = await supabase
      .from('organization_members')
      .select('role')
      .eq('organization_id', organization_id)
      .eq('user_id', req.user.id)
      .single();

    if (!membership) {
      return res.status(403).json({ error: 'Access denied to this organization' });
    }

    const { data: agents, error } = await supabase
      .from('agents')
      .select('*')
      .eq('organization_id', organization_id)
      .order('created_at', { ascending: false });

    if (error) {
      logger.error('Error fetching agents:', error);
      return res.status(500).json({ error: 'Failed to fetch agents' });
    }

    res.json({ agents: agents || [] });
  } catch (error) {
    logger.error('Error in GET /agents:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Create new agent
 */
router.post('/', validateCreateAgent, async (req, res) => {
  try {
    const { name, description, system_prompt, organization_id } = req.body;

    // Check organization admin access
    const { data: membership } = await supabase
      .from('organization_members')
      .select('role')
      .eq('organization_id', organization_id)
      .eq('user_id', req.user.id)
      .in('role', ['owner', 'admin'])
      .single();

    if (!membership) {
      return res.status(403).json({ error: 'Organization admin access required' });
    }

    // Create agent via Agent API
    try {
      const agentApiResponse = await axios.post(`${process.env.AGENT_API_URL}/api/agents/`, {
        name,
        description,
        system_prompt,
        organization_id
      }, {
        headers: {
          'Authorization': `Bearer ${process.env.AGENT_API_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      const { lyzr_agent_id } = agentApiResponse.data;

      // Store agent in our database
      const { data: agent, error } = await supabase
        .from('agents')
        .insert({
          lyzr_agent_id,
          organization_id,
          name,
          description,
          system_prompt,
          created_by: req.user.id
        })
        .select()
        .single();

      if (error) {
        logger.error('Error storing agent:', error);
        return res.status(500).json({ error: 'Failed to create agent' });
      }

      logger.info(`Agent created: ${agent.id} by user ${req.user.id}`);
      res.status(201).json(agent);
    } catch (apiError) {
      logger.error('Agent API error:', apiError.response?.data || apiError.message);
      res.status(500).json({ error: 'Failed to create agent via Agent API' });
    }
  } catch (error) {
    logger.error('Error in POST /agents:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Get specific agent
 */
router.get('/:agentId', validateUUIDParam('agentId'), async (req, res) => {
  try {
    const { agentId } = req.params;

    const { data: agent, error } = await supabase
      .from('agents')
      .select(`
        *,
        organizations!inner(
          id,
          name,
          organization_members!inner(
            user_id,
            role
          )
        )
      `)
      .eq('id', agentId)
      .eq('organizations.organization_members.user_id', req.user.id)
      .single();

    if (error || !agent) {
      return res.status(404).json({ error: 'Agent not found' });
    }

    res.json(agent);
  } catch (error) {
    logger.error('Error in GET /agents/:id:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Update agent
 */
router.put('/:agentId', 
  validateUUIDParam('agentId'),
  validateUpdateAgent,
  async (req, res) => {
    try {
      const { agentId } = req.params;
      const updates = req.body;

      // Get agent and check permissions
      const { data: agent, error: fetchError } = await supabase
        .from('agents')
        .select(`
          *,
          organizations!inner(
            id,
            organization_members!inner(
              user_id,
              role
            )
          )
        `)
        .eq('id', agentId)
        .eq('organizations.organization_members.user_id', req.user.id)
        .in('organizations.organization_members.role', ['owner', 'admin'])
        .single();

      if (fetchError || !agent) {
        return res.status(404).json({ error: 'Agent not found or access denied' });
      }

      // Update agent in our database
      const { data: updatedAgent, error } = await supabase
        .from('agents')
        .update(updates)
        .eq('id', agentId)
        .select()
        .single();

      if (error) {
        logger.error('Error updating agent:', error);
        return res.status(500).json({ error: 'Failed to update agent' });
      }

      // TODO: Update agent via Agent API as well

      logger.info(`Agent updated: ${agentId} by user ${req.user.id}`);
      res.json(updatedAgent);
    } catch (error) {
      logger.error('Error in PUT /agents/:id:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

/**
 * Delete agent
 */
router.delete('/:agentId', validateUUIDParam('agentId'), async (req, res) => {
  try {
    const { agentId } = req.params;

    // Get agent and check permissions
    const { data: agent, error: fetchError } = await supabase
      .from('agents')
      .select(`
        *,
        organizations!inner(
          id,
          organization_members!inner(
            user_id,
            role
          )
        )
      `)
      .eq('id', agentId)
      .eq('organizations.organization_members.user_id', req.user.id)
      .in('organizations.organization_members.role', ['owner', 'admin'])
      .single();

    if (fetchError || !agent) {
      return res.status(404).json({ error: 'Agent not found or access denied' });
    }

    // Delete agent from our database
    const { error } = await supabase
      .from('agents')
      .delete()
      .eq('id', agentId);

    if (error) {
      logger.error('Error deleting agent:', error);
      return res.status(500).json({ error: 'Failed to delete agent' });
    }

    // TODO: Delete agent via Agent API as well

    logger.info(`Agent deleted: ${agentId} by user ${req.user.id}`);
    res.json({ message: 'Agent deleted successfully' });
  } catch (error) {
    logger.error('Error in DELETE /agents/:id:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
