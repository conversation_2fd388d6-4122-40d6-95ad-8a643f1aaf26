/**
 * Organization management routes
 */
const express = require('express');
const { supabase } = require('../config/database');
const { requireAuth, loadUser, requireOrganizationMember, requireOrganizationAdmin } = require('../middleware/auth');
const { 
  validateCreateOrganization, 
  validateUpdateOrganization, 
  validateUUIDParam,
  validatePagination 
} = require('../middleware/validation');
const logger = require('../utils/logger');

const router = express.Router();

// Apply authentication to all routes
router.use(requireAuth);
router.use(loadUser);

/**
 * Get user's organizations
 */
router.get('/', validatePagination, async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    const { data: organizations, error, count } = await supabase
      .from('organizations')
      .select(`
        *,
        organization_members!inner(
          role,
          joined_at
        )
      `, { count: 'exact' })
      .eq('organization_members.user_id', req.user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      logger.error('Error fetching organizations:', error);
      return res.status(500).json({ error: 'Failed to fetch organizations' });
    }

    res.json({
      organizations: organizations || [],
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count || 0,
        pages: Math.ceil((count || 0) / limit)
      }
    });
  } catch (error) {
    logger.error('Error in GET /organizations:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Create new organization
 */
router.post('/', validateCreateOrganization, async (req, res) => {
  try {
    const { name, slug, description, website_url } = req.body;

    // Check if slug is already taken
    const { data: existingOrg } = await supabase
      .from('organizations')
      .select('id')
      .eq('slug', slug)
      .single();

    if (existingOrg) {
      return res.status(400).json({ error: 'Organization slug already exists' });
    }

    // Create organization
    const { data: organization, error: orgError } = await supabase
      .from('organizations')
      .insert({
        name,
        slug,
        description,
        website_url,
        owner_id: req.user.id
      })
      .select()
      .single();

    if (orgError) {
      logger.error('Error creating organization:', orgError);
      return res.status(500).json({ error: 'Failed to create organization' });
    }

    // Add user as owner member
    const { error: memberError } = await supabase
      .from('organization_members')
      .insert({
        organization_id: organization.id,
        user_id: req.user.id,
        role: 'owner'
      });

    if (memberError) {
      logger.error('Error adding organization member:', memberError);
      // Try to clean up the organization
      await supabase.from('organizations').delete().eq('id', organization.id);
      return res.status(500).json({ error: 'Failed to create organization' });
    }

    logger.info(`Organization created: ${organization.id} by user ${req.user.id}`);
    res.status(201).json(organization);
  } catch (error) {
    logger.error('Error in POST /organizations:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Get specific organization
 */
router.get('/:organizationId', 
  validateUUIDParam('organizationId'),
  requireOrganizationMember('organizationId'),
  async (req, res) => {
    try {
      const { organizationId } = req.params;

      const { data: organization, error } = await supabase
        .from('organizations')
        .select(`
          *,
          organization_members(
            user_id,
            role,
            joined_at,
            users(
              id,
              email,
              first_name,
              last_name
            )
          )
        `)
        .eq('id', organizationId)
        .single();

      if (error) {
        logger.error('Error fetching organization:', error);
        return res.status(500).json({ error: 'Failed to fetch organization' });
      }

      res.json(organization);
    } catch (error) {
      logger.error('Error in GET /organizations/:id:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

/**
 * Update organization
 */
router.put('/:organizationId',
  validateUUIDParam('organizationId'),
  validateUpdateOrganization,
  requireOrganizationAdmin('organizationId'),
  async (req, res) => {
    try {
      const { organizationId } = req.params;
      const updates = req.body;

      const { data: organization, error } = await supabase
        .from('organizations')
        .update(updates)
        .eq('id', organizationId)
        .select()
        .single();

      if (error) {
        logger.error('Error updating organization:', error);
        return res.status(500).json({ error: 'Failed to update organization' });
      }

      logger.info(`Organization updated: ${organizationId} by user ${req.user.id}`);
      res.json(organization);
    } catch (error) {
      logger.error('Error in PUT /organizations/:id:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

/**
 * Delete organization
 */
router.delete('/:organizationId',
  validateUUIDParam('organizationId'),
  requireOrganizationAdmin('organizationId'),
  async (req, res) => {
    try {
      const { organizationId } = req.params;

      // Only owner can delete organization
      if (req.organizationMembership.role !== 'owner') {
        return res.status(403).json({ error: 'Only organization owner can delete organization' });
      }

      const { error } = await supabase
        .from('organizations')
        .delete()
        .eq('id', organizationId);

      if (error) {
        logger.error('Error deleting organization:', error);
        return res.status(500).json({ error: 'Failed to delete organization' });
      }

      logger.info(`Organization deleted: ${organizationId} by user ${req.user.id}`);
      res.json({ message: 'Organization deleted successfully' });
    } catch (error) {
      logger.error('Error in DELETE /organizations/:id:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

/**
 * Get organization members
 */
router.get('/:organizationId/members',
  validateUUIDParam('organizationId'),
  requireOrganizationMember('organizationId'),
  async (req, res) => {
    try {
      const { organizationId } = req.params;

      const { data: members, error } = await supabase
        .from('organization_members')
        .select(`
          *,
          users(
            id,
            email,
            first_name,
            last_name,
            created_at
          )
        `)
        .eq('organization_id', organizationId)
        .order('joined_at', { ascending: false });

      if (error) {
        logger.error('Error fetching organization members:', error);
        return res.status(500).json({ error: 'Failed to fetch members' });
      }

      res.json({ members: members || [] });
    } catch (error) {
      logger.error('Error in GET /organizations/:id/members:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

module.exports = router;
