/**
 * Analytics and reporting routes
 */
const express = require('express');
const { supabase } = require('../config/database');
const { requireAuth, loadUser, requireOrganizationMember } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// Apply authentication to all routes
router.use(requireAuth);
router.use(loadUser);

/**
 * Get dashboard analytics for organization
 */
router.get('/dashboard', async (req, res) => {
  try {
    const { organization_id, period = '7d' } = req.query;
    
    if (!organization_id) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // Check organization membership
    const { data: membership } = await supabase
      .from('organization_members')
      .select('role')
      .eq('organization_id', organization_id)
      .eq('user_id', req.user.id)
      .single();

    if (!membership) {
      return res.status(403).json({ error: 'Access denied to this organization' });
    }

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    
    switch (period) {
      case '24h':
        startDate.setHours(startDate.getHours() - 24);
        break;
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(startDate.getDate() - 90);
        break;
      default:
        startDate.setDate(startDate.getDate() - 7);
    }

    // Get chat sessions count
    const { count: totalSessions } = await supabase
      .from('chat_sessions')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString())
      .eq('agents.organization_id', organization_id);

    // Get active sessions count
    const { count: activeSessions } = await supabase
      .from('chat_sessions')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'active')
      .eq('agents.organization_id', organization_id);

    // Get total messages count
    const { count: totalMessages } = await supabase
      .from('messages')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString())
      .eq('chat_sessions.agents.organization_id', organization_id);

    // Get tickets count by status
    const { data: ticketStats } = await supabase
      .from('tickets')
      .select('status')
      .eq('organization_id', organization_id)
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString());

    const ticketsByStatus = {
      open: 0,
      in_progress: 0,
      resolved: 0,
      closed: 0
    };

    ticketStats?.forEach(ticket => {
      ticketsByStatus[ticket.status] = (ticketsByStatus[ticket.status] || 0) + 1;
    });

    // Get agent performance
    const { data: agentStats } = await supabase
      .from('agents')
      .select(`
        id,
        name,
        chat_sessions(count)
      `)
      .eq('organization_id', organization_id);

    const analytics = {
      period,
      date_range: {
        start: startDate.toISOString(),
        end: endDate.toISOString()
      },
      chat_metrics: {
        total_sessions: totalSessions || 0,
        active_sessions: activeSessions || 0,
        total_messages: totalMessages || 0,
        avg_messages_per_session: totalSessions > 0 ? Math.round((totalMessages || 0) / totalSessions) : 0
      },
      ticket_metrics: {
        total_tickets: Object.values(ticketsByStatus).reduce((sum, count) => sum + count, 0),
        by_status: ticketsByStatus
      },
      agent_performance: agentStats || []
    };

    res.json(analytics);
  } catch (error) {
    logger.error('Error in GET /analytics/dashboard:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Get chat analytics
 */
router.get('/chat', async (req, res) => {
  try {
    const { organization_id, period = '7d', agent_id } = req.query;
    
    if (!organization_id) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // Check organization membership
    const { data: membership } = await supabase
      .from('organization_members')
      .select('role')
      .eq('organization_id', organization_id)
      .eq('user_id', req.user.id)
      .single();

    if (!membership) {
      return res.status(403).json({ error: 'Access denied to this organization' });
    }

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period.replace('d', '')));

    let query = supabase
      .from('chat_sessions')
      .select(`
        id,
        created_at,
        ended_at,
        message_count,
        status,
        agents!inner(
          id,
          name,
          organization_id
        )
      `)
      .eq('agents.organization_id', organization_id)
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString());

    if (agent_id) {
      query = query.eq('agent_id', agent_id);
    }

    const { data: sessions, error } = await query;

    if (error) {
      logger.error('Error fetching chat analytics:', error);
      return res.status(500).json({ error: 'Failed to fetch chat analytics' });
    }

    // Process analytics
    const analytics = {
      total_sessions: sessions?.length || 0,
      avg_session_duration: 0,
      avg_messages_per_session: 0,
      sessions_by_day: {},
      sessions_by_agent: {}
    };

    if (sessions && sessions.length > 0) {
      let totalDuration = 0;
      let totalMessages = 0;
      let sessionsWithDuration = 0;

      sessions.forEach(session => {
        // Duration calculation
        if (session.ended_at) {
          const duration = new Date(session.ended_at) - new Date(session.created_at);
          totalDuration += duration;
          sessionsWithDuration++;
        }

        // Messages
        totalMessages += session.message_count || 0;

        // By day
        const day = new Date(session.created_at).toISOString().split('T')[0];
        analytics.sessions_by_day[day] = (analytics.sessions_by_day[day] || 0) + 1;

        // By agent
        const agentName = session.agents.name;
        analytics.sessions_by_agent[agentName] = (analytics.sessions_by_agent[agentName] || 0) + 1;
      });

      analytics.avg_session_duration = sessionsWithDuration > 0 ? Math.round(totalDuration / sessionsWithDuration / 1000 / 60) : 0; // minutes
      analytics.avg_messages_per_session = Math.round(totalMessages / sessions.length);
    }

    res.json(analytics);
  } catch (error) {
    logger.error('Error in GET /analytics/chat:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Get ticket analytics
 */
router.get('/tickets', async (req, res) => {
  try {
    const { organization_id, period = '30d' } = req.query;
    
    if (!organization_id) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // Check organization membership
    const { data: membership } = await supabase
      .from('organization_members')
      .select('role')
      .eq('organization_id', organization_id)
      .eq('user_id', req.user.id)
      .single();

    if (!membership) {
      return res.status(403).json({ error: 'Access denied to this organization' });
    }

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period.replace('d', '')));

    const { data: tickets, error } = await supabase
      .from('tickets')
      .select(`
        id,
        status,
        priority,
        created_at,
        resolved_at,
        assigned_to
      `)
      .eq('organization_id', organization_id)
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString());

    if (error) {
      logger.error('Error fetching ticket analytics:', error);
      return res.status(500).json({ error: 'Failed to fetch ticket analytics' });
    }

    // Process analytics
    const analytics = {
      total_tickets: tickets?.length || 0,
      by_status: { open: 0, in_progress: 0, resolved: 0, closed: 0 },
      by_priority: { low: 0, medium: 0, high: 0, urgent: 0 },
      avg_resolution_time: 0,
      tickets_by_day: {}
    };

    if (tickets && tickets.length > 0) {
      let totalResolutionTime = 0;
      let resolvedTickets = 0;

      tickets.forEach(ticket => {
        // By status
        analytics.by_status[ticket.status] = (analytics.by_status[ticket.status] || 0) + 1;

        // By priority
        analytics.by_priority[ticket.priority] = (analytics.by_priority[ticket.priority] || 0) + 1;

        // Resolution time
        if (ticket.resolved_at) {
          const resolutionTime = new Date(ticket.resolved_at) - new Date(ticket.created_at);
          totalResolutionTime += resolutionTime;
          resolvedTickets++;
        }

        // By day
        const day = new Date(ticket.created_at).toISOString().split('T')[0];
        analytics.tickets_by_day[day] = (analytics.tickets_by_day[day] || 0) + 1;
      });

      analytics.avg_resolution_time = resolvedTickets > 0 ? Math.round(totalResolutionTime / resolvedTickets / 1000 / 60 / 60) : 0; // hours
    }

    res.json(analytics);
  } catch (error) {
    logger.error('Error in GET /analytics/tickets:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
