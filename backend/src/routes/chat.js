/**
 * Chat session and messaging routes
 */
const express = require('express');
const axios = require('axios');
const { supabase } = require('../config/database');
const { requireAuth, loadUser, optionalAuth } = require('../middleware/auth');
const { validateStartChatSession, validateSendMessage, validateUUIDParam } = require('../middleware/validation');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * Start new chat session (public endpoint for widget)
 */
router.post('/start', validateStartChatSession, async (req, res) => {
  try {
    const { widget_key, visitor_id, visitor_info, initial_message } = req.body;

    // Get widget and agent information
    const { data: widget, error: widgetError } = await supabase
      .from('chat_widgets')
      .select(`
        *,
        agents(
          id,
          lyzr_agent_id,
          name,
          organization_id
        )
      `)
      .eq('widget_key', widget_key)
      .eq('is_active', true)
      .single();

    if (widgetError || !widget) {
      return res.status(404).json({ error: 'Widget not found or inactive' });
    }

    // Create chat session
    const { data: session, error: sessionError } = await supabase
      .from('chat_sessions')
      .insert({
        widget_id: widget.id,
        agent_id: widget.agent_id,
        visitor_id: visitor_id || `visitor_${Date.now()}`,
        visitor_info: visitor_info || {},
        status: 'active'
      })
      .select()
      .single();

    if (sessionError) {
      logger.error('Error creating chat session:', sessionError);
      return res.status(500).json({ error: 'Failed to create chat session' });
    }

    // Start chat via Agent API if initial message provided
    let agentResponse = null;
    if (initial_message) {
      try {
        const chatResponse = await axios.post(`${process.env.AGENT_API_URL}/api/chat/start`, {
          agent_id: widget.agents.lyzr_agent_id,
          widget_key,
          visitor_id: session.visitor_id,
          visitor_info,
          initial_message
        }, {
          headers: {
            'Authorization': `Bearer ${process.env.AGENT_API_KEY}`,
            'Content-Type': 'application/json'
          }
        });

        agentResponse = chatResponse.data;

        // Store initial messages
        await supabase.from('messages').insert([
          {
            session_id: session.id,
            type: 'user',
            content: initial_message
          },
          {
            session_id: session.id,
            type: 'agent',
            content: agentResponse.agent_response
          }
        ]);

        // Update session message count
        await supabase
          .from('chat_sessions')
          .update({ message_count: 2 })
          .eq('id', session.id);

      } catch (apiError) {
        logger.error('Agent API error:', apiError.response?.data || apiError.message);
        // Continue without agent response
      }
    }

    logger.info(`Chat session started: ${session.id} for widget ${widget_key}`);
    res.status(201).json({
      session_id: session.id,
      agent_name: widget.agents.name,
      status: 'active',
      initial_response: agentResponse?.agent_response
    });
  } catch (error) {
    logger.error('Error in POST /chat/start:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Send message in chat session
 */
router.post('/message', validateSendMessage, async (req, res) => {
  try {
    const { session_id, message } = req.body;

    // Get session and agent information
    const { data: session, error: sessionError } = await supabase
      .from('chat_sessions')
      .select(`
        *,
        agents(
          lyzr_agent_id
        )
      `)
      .eq('id', session_id)
      .eq('status', 'active')
      .single();

    if (sessionError || !session) {
      return res.status(404).json({ error: 'Chat session not found or inactive' });
    }

    // Store user message
    const { error: messageError } = await supabase
      .from('messages')
      .insert({
        session_id,
        type: 'user',
        content: message
      });

    if (messageError) {
      logger.error('Error storing user message:', messageError);
      return res.status(500).json({ error: 'Failed to store message' });
    }

    // Send message to Agent API
    try {
      const chatResponse = await axios.post(`${process.env.AGENT_API_URL}/api/chat/message`, {
        session_id,
        message,
        message_type: 'user'
      }, {
        headers: {
          'Authorization': `Bearer ${process.env.AGENT_API_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      const agentResponse = chatResponse.data.agent_response;

      // Store agent response
      await supabase
        .from('messages')
        .insert({
          session_id,
          type: 'agent',
          content: agentResponse
        });

      // Update session message count
      await supabase
        .from('chat_sessions')
        .update({ 
          message_count: session.message_count + 2,
          last_message_at: new Date().toISOString()
        })
        .eq('id', session_id);

      logger.info(`Message processed in session ${session_id}`);
      res.json({
        status: 'success',
        agent_response: agentResponse,
        session_id,
        response_time: chatResponse.data.response_time
      });

    } catch (apiError) {
      logger.error('Agent API error:', apiError.response?.data || apiError.message);
      res.status(500).json({ error: 'Failed to get agent response' });
    }
  } catch (error) {
    logger.error('Error in POST /chat/message:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Get chat history
 */
router.get('/history/:sessionId', validateUUIDParam('sessionId'), async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { limit = 50, offset = 0 } = req.query;

    // Get session (public access for now)
    const { data: session, error: sessionError } = await supabase
      .from('chat_sessions')
      .select('id, visitor_id')
      .eq('id', sessionId)
      .single();

    if (sessionError || !session) {
      return res.status(404).json({ error: 'Chat session not found' });
    }

    // Get messages
    const { data: messages, error: messagesError, count } = await supabase
      .from('messages')
      .select('*', { count: 'exact' })
      .eq('session_id', sessionId)
      .order('created_at', { ascending: true })
      .range(parseInt(offset), parseInt(offset) + parseInt(limit) - 1);

    if (messagesError) {
      logger.error('Error fetching chat history:', messagesError);
      return res.status(500).json({ error: 'Failed to fetch chat history' });
    }

    res.json({
      session_id: sessionId,
      messages: messages || [],
      total_messages: count || 0,
      has_more: (count || 0) > parseInt(offset) + parseInt(limit)
    });
  } catch (error) {
    logger.error('Error in GET /chat/history/:sessionId:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * End chat session
 */
router.post('/end/:sessionId', validateUUIDParam('sessionId'), async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { reason } = req.body;

    // Update session status
    const { data: session, error } = await supabase
      .from('chat_sessions')
      .update({ 
        status: 'ended',
        ended_at: new Date().toISOString(),
        end_reason: reason
      })
      .eq('id', sessionId)
      .select()
      .single();

    if (error) {
      logger.error('Error ending chat session:', error);
      return res.status(500).json({ error: 'Failed to end chat session' });
    }

    // Notify Agent API
    try {
      await axios.post(`${process.env.AGENT_API_URL}/api/chat/end`, {
        session_id: sessionId,
        reason
      }, {
        headers: {
          'Authorization': `Bearer ${process.env.AGENT_API_KEY}`,
          'Content-Type': 'application/json'
        }
      });
    } catch (apiError) {
      logger.warn('Failed to notify Agent API of session end:', apiError.message);
    }

    logger.info(`Chat session ended: ${sessionId}`);
    res.json({ message: 'Chat session ended successfully' });
  } catch (error) {
    logger.error('Error in POST /chat/end/:sessionId:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Get chat sessions for organization (authenticated)
 */
router.get('/sessions', requireAuth, loadUser, async (req, res) => {
  try {
    const { organization_id, status, limit = 20, offset = 0 } = req.query;

    if (!organization_id) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // Check organization membership
    const { data: membership } = await supabase
      .from('organization_members')
      .select('role')
      .eq('organization_id', organization_id)
      .eq('user_id', req.user.id)
      .single();

    if (!membership) {
      return res.status(403).json({ error: 'Access denied to this organization' });
    }

    let query = supabase
      .from('chat_sessions')
      .select(`
        *,
        agents(
          id,
          name
        ),
        chat_widgets(
          id,
          name
        )
      `, { count: 'exact' })
      .eq('agents.organization_id', organization_id)
      .order('created_at', { ascending: false })
      .range(parseInt(offset), parseInt(offset) + parseInt(limit) - 1);

    if (status) {
      query = query.eq('status', status);
    }

    const { data: sessions, error, count } = await query;

    if (error) {
      logger.error('Error fetching chat sessions:', error);
      return res.status(500).json({ error: 'Failed to fetch chat sessions' });
    }

    res.json({
      sessions: sessions || [],
      total: count || 0,
      limit: parseInt(limit),
      offset: parseInt(offset)
    });
  } catch (error) {
    logger.error('Error in GET /chat/sessions:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
