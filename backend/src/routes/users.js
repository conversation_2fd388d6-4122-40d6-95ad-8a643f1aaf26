/**
 * User management routes
 */
const express = require('express');
const { supabase } = require('../config/database');
const { requireAuth, loadUser, requireAdmin } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// Apply authentication to all routes
router.use(requireAuth);
router.use(loadUser);

/**
 * Get current user profile
 */
router.get('/me', async (req, res) => {
  try {
    res.json({
      id: req.user.id,
      email: req.user.email,
      first_name: req.user.first_name,
      last_name: req.user.last_name,
      role: req.user.role,
      created_at: req.user.created_at,
      updated_at: req.user.updated_at
    });
  } catch (error) {
    logger.error('Error in GET /users/me:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Update current user profile
 */
router.put('/me', async (req, res) => {
  try {
    const { first_name, last_name } = req.body;
    
    const { data: user, error } = await supabase
      .from('users')
      .update({ first_name, last_name })
      .eq('id', req.user.id)
      .select()
      .single();

    if (error) {
      logger.error('Error updating user profile:', error);
      return res.status(500).json({ error: 'Failed to update profile' });
    }

    res.json(user);
  } catch (error) {
    logger.error('Error in PUT /users/me:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * Get all users (admin only)
 */
router.get('/', requireAdmin, async (req, res) => {
  try {
    const { data: users, error } = await supabase
      .from('users')
      .select('id, email, first_name, last_name, role, created_at')
      .order('created_at', { ascending: false });

    if (error) {
      logger.error('Error fetching users:', error);
      return res.status(500).json({ error: 'Failed to fetch users' });
    }

    res.json({ users: users || [] });
  } catch (error) {
    logger.error('Error in GET /users:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
