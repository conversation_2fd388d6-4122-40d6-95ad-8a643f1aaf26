/**
 * Database configuration and Supabase client setup
 */
const { createClient } = require('@supabase/supabase-js');
const logger = require('../utils/logger');

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase configuration. Please check your environment variables.');
}

// Create Supabase client for public operations
const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false
  }
});

// Create Supabase admin client for service operations
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

/**
 * Test database connection
 */
async function testConnection() {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('count', { count: 'exact', head: true });
    
    if (error) {
      logger.error('Database connection test failed:', error);
      return false;
    }
    
    logger.info('Database connection successful');
    return true;
  } catch (error) {
    logger.error('Database connection error:', error);
    return false;
  }
}

/**
 * Execute a database query with error handling
 */
async function executeQuery(queryFn) {
  try {
    const result = await queryFn();
    if (result.error) {
      logger.error('Database query error:', result.error);
      throw new Error(result.error.message);
    }
    return result;
  } catch (error) {
    logger.error('Database operation failed:', error);
    throw error;
  }
}

/**
 * Get user by Clerk ID
 */
async function getUserByClerkId(clerkUserId) {
  return executeQuery(async () => {
    return await supabase
      .from('users')
      .select('*')
      .eq('clerk_user_id', clerkUserId)
      .single();
  });
}

/**
 * Create or update user from Clerk webhook
 */
async function upsertUser(userData) {
  return executeQuery(async () => {
    return await supabaseAdmin
      .from('users')
      .upsert(userData, {
        onConflict: 'clerk_user_id'
      })
      .select()
      .single();
  });
}

/**
 * Get organization by ID with member check
 */
async function getOrganizationWithMember(orgId, userId) {
  return executeQuery(async () => {
    return await supabase
      .from('organizations')
      .select(`
        *,
        organization_members!inner(
          user_id,
          role
        )
      `)
      .eq('id', orgId)
      .eq('organization_members.user_id', userId)
      .single();
  });
}

module.exports = {
  supabase,
  supabaseAdmin,
  testConnection,
  executeQuery,
  getUserByClerkId,
  upsertUser,
  getOrganizationWithMember
};
