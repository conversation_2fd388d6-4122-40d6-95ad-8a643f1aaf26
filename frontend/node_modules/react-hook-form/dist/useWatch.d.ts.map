{"version": 3, "file": "useWatch.d.ts", "sourceRoot": "", "sources": ["../src/useWatch.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,EACV,OAAO,EACP,uBAAuB,EACvB,SAAS,EACT,cAAc,EACd,eAAe,EACf,WAAW,EAGZ,MAAM,SAAS,CAAC;AAIjB;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,wBAAgB,QAAQ,CACtB,YAAY,SAAS,WAAW,GAAG,WAAW,EAC9C,kBAAkB,GAAG,YAAY,EACjC,KAAK,EAAE;IACP,IAAI,CAAC,EAAE,SAAS,CAAC;IACjB,YAAY,CAAC,EAAE,uBAAuB,CAAC,YAAY,CAAC,CAAC;IACrD,OAAO,CAAC,EAAE,OAAO,CAAC,YAAY,EAAE,GAAG,EAAE,kBAAkB,CAAC,CAAC;IACzD,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB,OAAO,CAAC,EAAE,SAAS,CAAC;CACrB,GAAG,uBAAuB,CAAC,YAAY,CAAC,CAAC;AAC1C;;;;;;;;;;;;;;;;;;;GAmBG;AACH,wBAAgB,QAAQ,CACtB,YAAY,SAAS,WAAW,GAAG,WAAW,EAC9C,UAAU,SAAS,SAAS,CAAC,YAAY,CAAC,GAAG,SAAS,CAAC,YAAY,CAAC,EACpE,kBAAkB,GAAG,YAAY,EACjC,KAAK,EAAE;IACP,IAAI,EAAE,UAAU,CAAC;IACjB,YAAY,CAAC,EAAE,cAAc,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;IACxD,OAAO,CAAC,EAAE,OAAO,CAAC,YAAY,EAAE,GAAG,EAAE,kBAAkB,CAAC,CAAC;IACzD,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB,OAAO,CAAC,EAAE,SAAS,CAAC;CACrB,GAAG,cAAc,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;AAC7C;;;;;;;;;;;;;;;;;GAiBG;AACH,wBAAgB,QAAQ,CACtB,YAAY,SAAS,WAAW,GAAG,WAAW,EAC9C,kBAAkB,GAAG,YAAY,EACjC,aAAa,GAAG,OAAO,EACvB,KAAK,EAAE;IACP,IAAI,CAAC,EAAE,SAAS,CAAC;IACjB,YAAY,CAAC,EAAE,uBAAuB,CAAC,YAAY,CAAC,CAAC;IACrD,OAAO,CAAC,EAAE,OAAO,CAAC,YAAY,EAAE,GAAG,EAAE,kBAAkB,CAAC,CAAC;IACzD,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB,OAAO,EAAE,CAAC,UAAU,EAAE,YAAY,KAAK,aAAa,CAAC;CACtD,GAAG,aAAa,CAAC;AAClB;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,wBAAgB,QAAQ,CACtB,YAAY,SAAS,WAAW,GAAG,WAAW,EAC9C,UAAU,SAAS,SAAS,CAAC,YAAY,CAAC,GAAG,SAAS,CAAC,YAAY,CAAC,EACpE,kBAAkB,GAAG,YAAY,EACjC,aAAa,GAAG,OAAO,EACvB,KAAK,EAAE;IACP,IAAI,EAAE,UAAU,CAAC;IACjB,YAAY,CAAC,EAAE,cAAc,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;IACxD,OAAO,CAAC,EAAE,OAAO,CAAC,YAAY,EAAE,GAAG,EAAE,kBAAkB,CAAC,CAAC;IACzD,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB,OAAO,EAAE,CACP,UAAU,EAAE,cAAc,CAAC,YAAY,EAAE,UAAU,CAAC,KACjD,aAAa,CAAC;CACpB,GAAG,aAAa,CAAC;AAClB;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,wBAAgB,QAAQ,CACtB,YAAY,SAAS,WAAW,GAAG,WAAW,EAC9C,WAAW,SACT,SAAS,SAAS,CAAC,YAAY,CAAC,EAAE,GAAG,SAAS,SAAS,CAAC,YAAY,CAAC,EAAE,EACzE,kBAAkB,GAAG,YAAY,EACjC,KAAK,EAAE;IACP,IAAI,EAAE,SAAS,CAAC,GAAG,WAAW,CAAC,CAAC;IAChC,YAAY,CAAC,EAAE,uBAAuB,CAAC,YAAY,CAAC,CAAC;IACrD,OAAO,CAAC,EAAE,OAAO,CAAC,YAAY,EAAE,GAAG,EAAE,kBAAkB,CAAC,CAAC;IACzD,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB,OAAO,CAAC,EAAE,SAAS,CAAC;CACrB,GAAG,eAAe,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;AAC/C;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACH,wBAAgB,QAAQ,CACtB,YAAY,SAAS,WAAW,GAAG,WAAW,EAC9C,WAAW,SACT,SAAS,SAAS,CAAC,YAAY,CAAC,EAAE,GAAG,SAAS,SAAS,CAAC,YAAY,CAAC,EAAE,EACzE,kBAAkB,GAAG,YAAY,EACjC,aAAa,GAAG,OAAO,EACvB,KAAK,EAAE;IACP,IAAI,EAAE,SAAS,CAAC,GAAG,WAAW,CAAC,CAAC;IAChC,YAAY,CAAC,EAAE,uBAAuB,CAAC,YAAY,CAAC,CAAC;IACrD,OAAO,CAAC,EAAE,OAAO,CAAC,YAAY,EAAE,GAAG,EAAE,kBAAkB,CAAC,CAAC;IACzD,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB,OAAO,EAAE,CACP,UAAU,EAAE,eAAe,CAAC,YAAY,EAAE,WAAW,CAAC,KACnD,aAAa,CAAC;CACpB,GAAG,aAAa,CAAC;AAClB;;;;;;;;;;;;GAYG;AACH,wBAAgB,QAAQ,CACtB,YAAY,SAAS,WAAW,GAAG,WAAW,KAC3C,uBAAuB,CAAC,YAAY,CAAC,CAAC"}