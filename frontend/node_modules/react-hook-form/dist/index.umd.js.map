{"version": 3, "file": "index.umd.js", "sources": ["../src/utils/isCheckBoxInput.ts", "../src/utils/isDateObject.ts", "../src/utils/isNullOrUndefined.ts", "../src/utils/isObject.ts", "../src/logic/getEventValue.ts", "../src/logic/isNameInFieldArray.ts", "../src/logic/getNodeParentName.ts", "../src/utils/isWeb.ts", "../src/utils/cloneObject.ts", "../src/utils/isPlainObject.ts", "../src/utils/isKey.ts", "../src/utils/isUndefined.ts", "../src/utils/compact.ts", "../src/utils/stringToPath.ts", "../src/utils/get.ts", "../src/utils/isBoolean.ts", "../src/utils/set.ts", "../src/constants.ts", "../src/useFormContext.tsx", "../src/logic/getProxyFormState.ts", "../src/useIsomorphicLayoutEffect.ts", "../src/useFormState.ts", "../src/utils/isString.ts", "../src/logic/generateWatchOutput.ts", "../src/utils/isPrimitive.ts", "../src/utils/deepEqual.ts", "../src/useWatch.ts", "../src/useController.ts", "../src/controller.tsx", "../src/utils/flatten.ts", "../src/form.tsx", "../src/logic/appendErrors.ts", "../src/utils/convertToArrayPayload.ts", "../src/utils/createSubject.ts", "../src/utils/isEmptyObject.ts", "../src/utils/isFileInput.ts", "../src/utils/isFunction.ts", "../src/utils/isHTMLElement.ts", "../src/utils/isMultipleSelect.ts", "../src/utils/isRadioInput.ts", "../src/utils/live.ts", "../src/utils/unset.ts", "../src/utils/objectHasFunction.ts", "../src/logic/getDirtyFields.ts", "../src/logic/getCheckboxValue.ts", "../src/logic/getFieldValueAs.ts", "../src/logic/getRadioValue.ts", "../src/logic/getFieldValue.ts", "../src/logic/getResolverOptions.ts", "../src/utils/isRegex.ts", "../src/logic/getRuleValue.ts", "../src/logic/getValidationModes.ts", "../src/logic/hasPromiseValidation.ts", "../src/logic/isWatched.ts", "../src/logic/iterateFieldsByAction.ts", "../src/logic/schemaErrorLookup.ts", "../src/logic/shouldRenderFormState.ts", "../src/logic/updateFieldArrayRootError.ts", "../src/utils/isMessage.ts", "../src/logic/getValidateError.ts", "../src/logic/getValueAndMessage.ts", "../src/logic/validateField.ts", "../src/logic/createFormControl.ts", "../src/logic/hasValidation.ts", "../src/logic/skipValidation.ts", "../src/logic/shouldSubscribeByName.ts", "../src/utils/isRadioOrCheckbox.ts", "../src/logic/unsetEmptyArray.ts", "../src/logic/generateId.ts", "../src/logic/getFocusFieldName.ts", "../src/utils/append.ts", "../src/utils/fillEmptyArray.ts", "../src/utils/insert.ts", "../src/utils/move.ts", "../src/utils/prepend.ts", "../src/utils/remove.ts", "../src/utils/swap.ts", "../src/utils/update.ts", "../src/useFieldArray.ts", "../src/useForm.ts"], "sourcesContent": ["import type { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'checkbox';\n", "export default (value: unknown): value is Date => value instanceof Date;\n", "export default (value: unknown): value is null | undefined => value == null;\n", "import isDateObject from './isDateObject';\nimport isNullOrUndefined from './isNullOrUndefined';\n\nexport const isObjectType = (value: unknown): value is object =>\n  typeof value === 'object';\n\nexport default <T extends object>(value: unknown): value is T =>\n  !isNullOrUndefined(value) &&\n  !Array.isArray(value) &&\n  isObjectType(value) &&\n  !isDateObject(value);\n", "import isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isObject from '../utils/isObject';\n\ntype Event = { target: any };\n\nexport default (event: unknown) =>\n  isObject(event) && (event as Event).target\n    ? isCheckBoxInput((event as Event).target)\n      ? (event as Event).target.checked\n      : (event as Event).target.value\n    : event;\n", "import type { InternalFieldName } from '../types';\n\nimport getNodeParentName from './getNodeParentName';\n\nexport default (names: Set<InternalFieldName>, name: InternalFieldName) =>\n  names.has(getNodeParentName(name));\n", "export default (name: string) =>\n  name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\n", "export default typeof window !== 'undefined' &&\n  typeof window.HTMLElement !== 'undefined' &&\n  typeof document !== 'undefined';\n", "import isObject from './isObject';\nimport isPlainObject from './isPlainObject';\nimport isWeb from './isWeb';\n\nexport default function cloneObject<T>(data: T): T {\n  let copy: any;\n  const isArray = Array.isArray(data);\n  const isFileListInstance =\n    typeof FileList !== 'undefined' ? data instanceof FileList : false;\n\n  if (data instanceof Date) {\n    copy = new Date(data);\n  } else if (\n    !(isWeb && (data instanceof Blob || isFileListInstance)) &&\n    (isArray || isObject(data))\n  ) {\n    copy = isArray ? [] : {};\n\n    if (!isArray && !isPlainObject(data)) {\n      copy = data;\n    } else {\n      for (const key in data) {\n        if (data.hasOwnProperty(key)) {\n          copy[key] = cloneObject(data[key]);\n        }\n      }\n    }\n  } else {\n    return data;\n  }\n\n  return copy;\n}\n", "import isObject from './isObject';\n\nexport default (tempObject: object) => {\n  const prototypeCopy =\n    tempObject.constructor && tempObject.constructor.prototype;\n\n  return (\n    isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf')\n  );\n};\n", "export default (value: string) => /^\\w*$/.test(value);\n", "export default (val: unknown): val is undefined => val === undefined;\n", "export default <TValue>(value: TValue[]) =>\n  Array.isArray(value) ? value.filter(Boolean) : [];\n", "import compact from './compact';\n\nexport default (input: string): string[] =>\n  compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\n", "import isKey from './isKey';\nimport isNullOrUndefined from './isNullOrUndefined';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\nimport stringToPath from './stringToPath';\n\nexport default <T>(\n  object: T,\n  path?: string | null,\n  defaultValue?: unknown,\n): any => {\n  if (!path || !isObject(object)) {\n    return defaultValue;\n  }\n\n  const result = (isKey(path) ? [path] : stringToPath(path)).reduce(\n    (result, key) =>\n      isNullOrUndefined(result) ? result : result[key as keyof {}],\n    object,\n  );\n\n  return isUndefined(result) || result === object\n    ? isUndefined(object[path as keyof T])\n      ? defaultValue\n      : object[path as keyof T]\n    : result;\n};\n", "export default (value: unknown): value is boolean => typeof value === 'boolean';\n", "import type { FieldPath, FieldValues } from '../types';\n\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport stringToPath from './stringToPath';\n\nexport default (\n  object: FieldValues,\n  path: FieldPath<FieldValues>,\n  value?: unknown,\n) => {\n  let index = -1;\n  const tempPath = isKey(path) ? [path] : stringToPath(path);\n  const length = tempPath.length;\n  const lastIndex = length - 1;\n\n  while (++index < length) {\n    const key = tempPath[index];\n    let newValue = value;\n\n    if (index !== lastIndex) {\n      const objValue = object[key];\n      newValue =\n        isObject(objValue) || Array.isArray(objValue)\n          ? objValue\n          : !isNaN(+tempPath[index + 1])\n            ? []\n            : {};\n    }\n\n    if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n      return;\n    }\n\n    object[key] = newValue;\n    object = object[key];\n  }\n};\n", "export const EVENTS = {\n  BLUR: 'blur',\n  FOCUS_OUT: 'focusout',\n  CHANGE: 'change',\n} as const;\n\nexport const VALIDATION_MODE = {\n  onBlur: 'onBlur',\n  onChange: 'onChange',\n  onSubmit: 'onSubmit',\n  onTouched: 'onTouched',\n  all: 'all',\n} as const;\n\nexport const INPUT_VALIDATION_RULES = {\n  max: 'max',\n  min: 'min',\n  maxLength: 'maxLength',\n  minLength: 'minLength',\n  pattern: 'pattern',\n  required: 'required',\n  validate: 'validate',\n} as const;\n", "import React from 'react';\n\nimport type { FieldValues, FormProviderProps, UseFormReturn } from './types';\n\nconst HookFormContext = React.createContext<UseFormReturn | null>(null);\nHookFormContext.displayName = 'HookFormContext';\n\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const useFormContext = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(): UseFormReturn<TFieldValues, TContext, TTransformedValues> =>\n  React.useContext(HookFormContext) as UseFormReturn<\n    TFieldValues,\n    TContext,\n    TTransformedValues\n  >;\n\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const FormProvider = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: FormProviderProps<TFieldValues, TContext, TTransformedValues>,\n) => {\n  const { children, ...data } = props;\n  return (\n    <HookFormContext.Provider value={data as unknown as UseFormReturn}>\n      {children}\n    </HookFormContext.Provider>\n  );\n};\n", "import { VALIDATION_MODE } from '../constants';\nimport type { Control, FieldValues, FormState, ReadFormState } from '../types';\n\nexport default <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  formState: FormState<TFieldValues>,\n  control: Control<TFieldValues, TContext, TTransformedValues>,\n  localProxyFormState?: ReadFormState,\n  isRoot = true,\n) => {\n  const result = {\n    defaultValues: control._defaultValues,\n  } as typeof formState;\n\n  for (const key in formState) {\n    Object.defineProperty(result, key, {\n      get: () => {\n        const _key = key as keyof FormState<TFieldValues> & keyof ReadFormState;\n\n        if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n          control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n        }\n\n        localProxyFormState && (localProxyFormState[_key] = true);\n        return formState[_key];\n      },\n    });\n  }\n\n  return result;\n};\n", "import React from 'react';\n\nexport const useIsomorphicLayoutEffect =\n  typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n", "import React from 'react';\n\nimport getProxyFormState from './logic/getProxyFormState';\nimport type {\n  FieldValues,\n  UseFormStateProps,\n  UseFormStateReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useFormState<\n  TFieldValues extends FieldValues = FieldValues,\n  TTransformedValues = TFieldValues,\n>(\n  props?: UseFormStateProps<TFieldValues, TTransformedValues>,\n): UseFormStateReturn<TFieldValues> {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const { control = methods.control, disabled, name, exact } = props || {};\n  const [formState, updateFormState] = React.useState(control._formState);\n  const _localProxyFormState = React.useRef({\n    isDirty: false,\n    isLoading: false,\n    dirtyFields: false,\n    touchedFields: false,\n    validatingFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  });\n\n  useIsomorphicLayoutEffect(\n    () =>\n      control._subscribe({\n        name,\n        formState: _localProxyFormState.current,\n        exact,\n        callback: (formState) => {\n          !disabled &&\n            updateFormState({\n              ...control._formState,\n              ...formState,\n            });\n        },\n      }),\n    [name, disabled, exact],\n  );\n\n  React.useEffect(() => {\n    _localProxyFormState.current.isValid && control._setValid(true);\n  }, [control]);\n\n  return React.useMemo(\n    () =>\n      getProxyFormState(\n        formState,\n        control,\n        _localProxyFormState.current,\n        false,\n      ),\n    [formState, control],\n  );\n}\n", "export default (value: unknown): value is string => typeof value === 'string';\n", "import type { DeepPartial, FieldValues, Names } from '../types';\nimport get from '../utils/get';\nimport isString from '../utils/isString';\n\nexport default <T>(\n  names: string | string[] | undefined,\n  _names: Names,\n  formValues?: FieldValues,\n  isGlobal?: boolean,\n  defaultValue?: DeepPartial<T> | unknown,\n) => {\n  if (isString(names)) {\n    isGlobal && _names.watch.add(names);\n    return get(formValues, names, defaultValue);\n  }\n\n  if (Array.isArray(names)) {\n    return names.map(\n      (fieldName) => (\n        isGlobal && _names.watch.add(fieldName),\n        get(formValues, fieldName)\n      ),\n    );\n  }\n\n  isGlobal && (_names.watchAll = true);\n\n  return formValues;\n};\n", "import type { Primitive } from '../types';\n\nimport isNullOrUndefined from './isNullOrUndefined';\nimport { isObjectType } from './isObject';\n\nexport default (value: unknown): value is Primitive =>\n  isNullOrUndefined(value) || !isObjectType(value);\n", "import isObject from '../utils/isObject';\n\nimport isDateObject from './isDateObject';\nimport isPrimitive from './isPrimitive';\n\nexport default function deepEqual(\n  object1: any,\n  object2: any,\n  _internal_visited = new WeakSet(),\n) {\n  if (isPrimitive(object1) || isPrimitive(object2)) {\n    return object1 === object2;\n  }\n\n  if (isDateObject(object1) && isDateObject(object2)) {\n    return object1.getTime() === object2.getTime();\n  }\n\n  const keys1 = Object.keys(object1);\n  const keys2 = Object.keys(object2);\n\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n\n  if (_internal_visited.has(object1) || _internal_visited.has(object2)) {\n    return true;\n  }\n  _internal_visited.add(object1);\n  _internal_visited.add(object2);\n\n  for (const key of keys1) {\n    const val1 = object1[key];\n\n    if (!keys2.includes(key)) {\n      return false;\n    }\n\n    if (key !== 'ref') {\n      const val2 = object2[key];\n\n      if (\n        (isDateObject(val1) && isDateObject(val2)) ||\n        (isObject(val1) && isObject(val2)) ||\n        (Array.isArray(val1) && Array.isArray(val2))\n          ? !deepEqual(val1, val2, _internal_visited)\n          : val1 !== val2\n      ) {\n        return false;\n      }\n    }\n  }\n\n  return true;\n}\n", "import React from 'react';\n\nimport generateWatchOutput from './logic/generateWatchOutput';\nimport deepEqual from './utils/deepEqual';\nimport type {\n  Control,\n  DeepPartialSkipArrayKey,\n  FieldPath,\n  FieldPathValue,\n  FieldPathValues,\n  FieldValues,\n  InternalFieldName,\n  UseWatchProps,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * Subscribe to the entire form values change and re-render at the hook level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   defaultValue: {\n *     name: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TTransformedValues = TFieldValues,\n>(props: {\n  name?: undefined;\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n  compute?: undefined;\n}): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: \"fieldA\",\n *   defaultValue: \"default value\",\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(props: {\n  name: TFieldName;\n  defaultValue?: FieldPathValue<TFieldValues, TFieldName>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n  compute?: undefined;\n}): FieldPathValue<TFieldValues, TFieldName>;\n/**\n * Custom hook to subscribe to field change and compute function to produce state update\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   compute: (formValues) => formValues.fieldA\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TTransformedValues = TFieldValues,\n  TComputeValue = unknown,\n>(props: {\n  name?: undefined;\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n  compute: (formValues: TFieldValues) => TComputeValue;\n}): TComputeValue;\n/**\n * Custom hook to subscribe to field change and compute function to produce state update\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: \"fieldA\",\n *   defaultValue: \"default value\",\n *   exact: false,\n *   compute: (fieldValue) => fieldValue === \"data\" ? fieldValue : null,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n  TComputeValue = unknown,\n>(props: {\n  name: TFieldName;\n  defaultValue?: FieldPathValue<TFieldValues, TFieldName>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n  compute: (\n    fieldValue: FieldPathValue<TFieldValues, TFieldName>,\n  ) => TComputeValue;\n}): TComputeValue;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: [\"fieldA\", \"fieldB\"],\n *   defaultValue: {\n *     fieldA: \"data\",\n *     fieldB: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldNames extends\n    readonly FieldPath<TFieldValues>[] = readonly FieldPath<TFieldValues>[],\n  TTransformedValues = TFieldValues,\n>(props: {\n  name: readonly [...TFieldNames];\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n  compute?: undefined;\n}): FieldPathValues<TFieldValues, TFieldNames>;\n/**\n * Custom hook to subscribe to field change and compute function to produce state update\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: [\"fieldA\", \"fieldB\"],\n *   defaultValue: {\n *     fieldA: \"data\",\n *     fieldB: 0\n *   },\n *   compute: ([fieldAValue, fieldBValue]) => fieldB === 2 ? fieldA : null,\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldNames extends\n    readonly FieldPath<TFieldValues>[] = readonly FieldPath<TFieldValues>[],\n  TTransformedValues = TFieldValues,\n  TComputeValue = unknown,\n>(props: {\n  name: readonly [...TFieldNames];\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n  compute: (\n    fieldValue: FieldPathValues<TFieldValues, TFieldNames>,\n  ) => TComputeValue;\n}): TComputeValue;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * // can skip passing down the control into useWatch if the form is wrapped with the FormProvider\n * const values = useWatch()\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n>(): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nexport function useWatch<TFieldValues extends FieldValues>(\n  props?: UseWatchProps<TFieldValues>,\n) {\n  const methods = useFormContext<TFieldValues>();\n  const {\n    control = methods.control,\n    name,\n    defaultValue,\n    disabled,\n    exact,\n    compute,\n  } = props || {};\n  const _defaultValue = React.useRef(defaultValue);\n  const _compute = React.useRef(compute);\n  const _computeFormValues = React.useRef(undefined);\n\n  _compute.current = compute;\n\n  const defaultValueMemo = React.useMemo(\n    () =>\n      control._getWatch(\n        name as InternalFieldName,\n        _defaultValue.current as DeepPartialSkipArrayKey<TFieldValues>,\n      ),\n    [control, name],\n  );\n\n  const [value, updateValue] = React.useState(\n    _compute.current ? _compute.current(defaultValueMemo) : defaultValueMemo,\n  );\n\n  useIsomorphicLayoutEffect(\n    () =>\n      control._subscribe({\n        name,\n        formState: {\n          values: true,\n        },\n        exact,\n        callback: (formState) => {\n          if (!disabled) {\n            const formValues = generateWatchOutput(\n              name as InternalFieldName | InternalFieldName[],\n              control._names,\n              formState.values || control._formValues,\n              false,\n              _defaultValue.current,\n            );\n\n            if (_compute.current) {\n              const computedFormValues = _compute.current(formValues);\n\n              if (!deepEqual(computedFormValues, _computeFormValues.current)) {\n                updateValue(computedFormValues);\n                _computeFormValues.current = computedFormValues;\n              }\n            } else {\n              updateValue(formValues);\n            }\n          }\n        },\n      }),\n    [control, disabled, name, exact],\n  );\n\n  React.useEffect(() => control._removeUnmounted());\n\n  return value;\n}\n", "import React from 'react';\n\nimport getEventValue from './logic/getEventValue';\nimport isNameInFieldArray from './logic/isNameInFieldArray';\nimport cloneObject from './utils/cloneObject';\nimport get from './utils/get';\nimport isBoolean from './utils/isBoolean';\nimport isUndefined from './utils/isUndefined';\nimport set from './utils/set';\nimport { EVENTS } from './constants';\nimport type {\n  ControllerFieldState,\n  Field,\n  FieldPath,\n  FieldPathValue,\n  FieldValues,\n  InternalFieldName,\n  UseControllerProps,\n  UseControllerReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useFormState } from './useFormState';\nimport { useWatch } from './useWatch';\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nexport function useController<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseControllerProps<TFieldValues, TName, TTransformedValues>,\n): UseControllerReturn<TFieldValues, TName> {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const {\n    name,\n    disabled,\n    control = methods.control,\n    shouldUnregister,\n    defaultValue,\n  } = props;\n  const isArrayField = isNameInFieldArray(control._names.array, name);\n\n  const defaultValueMemo = React.useMemo(\n    () =>\n      get(\n        control._formValues,\n        name,\n        get(control._defaultValues, name, defaultValue),\n      ),\n    [control, name, defaultValue],\n  );\n\n  const value = useWatch({\n    control,\n    name,\n    defaultValue: defaultValueMemo,\n    exact: true,\n  }) as FieldPathValue<TFieldValues, TName>;\n\n  const formState = useFormState({\n    control,\n    name,\n    exact: true,\n  });\n\n  const _props = React.useRef(props);\n\n  const _registerProps = React.useRef(\n    control.register(name, {\n      ...props.rules,\n      value,\n      ...(isBoolean(props.disabled) ? { disabled: props.disabled } : {}),\n    }),\n  );\n\n  _props.current = props;\n\n  const fieldState = React.useMemo(\n    () =>\n      Object.defineProperties(\n        {},\n        {\n          invalid: {\n            enumerable: true,\n            get: () => !!get(formState.errors, name),\n          },\n          isDirty: {\n            enumerable: true,\n            get: () => !!get(formState.dirtyFields, name),\n          },\n          isTouched: {\n            enumerable: true,\n            get: () => !!get(formState.touchedFields, name),\n          },\n          isValidating: {\n            enumerable: true,\n            get: () => !!get(formState.validatingFields, name),\n          },\n          error: {\n            enumerable: true,\n            get: () => get(formState.errors, name),\n          },\n        },\n      ) as ControllerFieldState,\n    [formState, name],\n  );\n\n  const onChange = React.useCallback(\n    (event: any) =>\n      _registerProps.current.onChange({\n        target: {\n          value: getEventValue(event),\n          name: name as InternalFieldName,\n        },\n        type: EVENTS.CHANGE,\n      }),\n    [name],\n  );\n\n  const onBlur = React.useCallback(\n    () =>\n      _registerProps.current.onBlur({\n        target: {\n          value: get(control._formValues, name),\n          name: name as InternalFieldName,\n        },\n        type: EVENTS.BLUR,\n      }),\n    [name, control._formValues],\n  );\n\n  const ref = React.useCallback(\n    (elm: any) => {\n      const field = get(control._fields, name);\n\n      if (field && elm) {\n        field._f.ref = {\n          focus: () => elm.focus && elm.focus(),\n          select: () => elm.select && elm.select(),\n          setCustomValidity: (message: string) =>\n            elm.setCustomValidity(message),\n          reportValidity: () => elm.reportValidity(),\n        };\n      }\n    },\n    [control._fields, name],\n  );\n\n  const field = React.useMemo(\n    () => ({\n      name,\n      value,\n      ...(isBoolean(disabled) || formState.disabled\n        ? { disabled: formState.disabled || disabled }\n        : {}),\n      onChange,\n      onBlur,\n      ref,\n    }),\n    [name, disabled, formState.disabled, onChange, onBlur, ref, value],\n  );\n\n  React.useEffect(() => {\n    const _shouldUnregisterField =\n      control._options.shouldUnregister || shouldUnregister;\n\n    control.register(name, {\n      ..._props.current.rules,\n      ...(isBoolean(_props.current.disabled)\n        ? { disabled: _props.current.disabled }\n        : {}),\n    });\n\n    const updateMounted = (name: InternalFieldName, value: boolean) => {\n      const field: Field = get(control._fields, name);\n\n      if (field && field._f) {\n        field._f.mount = value;\n      }\n    };\n\n    updateMounted(name, true);\n\n    if (_shouldUnregisterField) {\n      const value = cloneObject(get(control._options.defaultValues, name));\n      set(control._defaultValues, name, value);\n      if (isUndefined(get(control._formValues, name))) {\n        set(control._formValues, name, value);\n      }\n    }\n\n    !isArrayField && control.register(name);\n\n    return () => {\n      (\n        isArrayField\n          ? _shouldUnregisterField && !control._state.action\n          : _shouldUnregisterField\n      )\n        ? control.unregister(name)\n        : updateMounted(name, false);\n    };\n  }, [name, control, isArrayField, shouldUnregister]);\n\n  React.useEffect(() => {\n    control._setDisabledField({\n      disabled,\n      name,\n    });\n  }, [disabled, name, control]);\n\n  return React.useMemo(\n    () => ({\n      field,\n      formState,\n      fieldState,\n    }),\n    [field, formState, fieldState],\n  );\n}\n", "import type { ControllerProps, FieldPath, FieldValues } from './types';\nimport { useController } from './useController';\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(\n  props: ControllerProps<TFieldValues, TName, TTransformedValues>,\n) =>\n  props.render(useController<TFieldValues, TName, TTransformedValues>(props));\n\nexport { Controller };\n", "import type { FieldValues } from '../types';\n\nimport { isObjectType } from './isObject';\n\nexport const flatten = (obj: FieldValues) => {\n  const output: FieldValues = {};\n\n  for (const key of Object.keys(obj)) {\n    if (isObjectType(obj[key]) && obj[key] !== null) {\n      const nested = flatten(obj[key]);\n\n      for (const nestedKey of Object.keys(nested)) {\n        output[`${key}.${nestedKey}`] = nested[nestedKey];\n      }\n    } else {\n      output[key] = obj[key];\n    }\n  }\n\n  return output;\n};\n", "import React from 'react';\n\nimport { flatten } from './utils/flatten';\nimport type { FieldValues, FormProps } from './types';\nimport { useFormContext } from './useFormContext';\n\nconst POST_REQUEST = 'post';\n\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form<\n  TFieldValues extends FieldValues,\n  TTransformedValues = TFieldValues,\n>(props: FormProps<TFieldValues, TTransformedValues>) {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const [mounted, setMounted] = React.useState(false);\n  const {\n    control = methods.control,\n    onSubmit,\n    children,\n    action,\n    method = POST_REQUEST,\n    headers,\n    encType,\n    onError,\n    render,\n    onSuccess,\n    validateStatus,\n    ...rest\n  } = props;\n\n  const submit = async (event?: React.BaseSyntheticEvent) => {\n    let hasError = false;\n    let type = '';\n\n    await control.handleSubmit(async (data) => {\n      const formData = new FormData();\n      let formDataJson = '';\n\n      try {\n        formDataJson = JSON.stringify(data);\n      } catch {}\n\n      const flattenFormValues = flatten(control._formValues);\n\n      for (const key in flattenFormValues) {\n        formData.append(key, flattenFormValues[key]);\n      }\n\n      if (onSubmit) {\n        await onSubmit({\n          data,\n          event,\n          method,\n          formData,\n          formDataJson,\n        });\n      }\n\n      if (action) {\n        try {\n          const shouldStringifySubmissionData = [\n            headers && headers['Content-Type'],\n            encType,\n          ].some((value) => value && value.includes('json'));\n\n          const response = await fetch(String(action), {\n            method,\n            headers: {\n              ...headers,\n              ...(encType && encType !== 'multipart/form-data'\n                ? { 'Content-Type': encType }\n                : {}),\n            },\n            body: shouldStringifySubmissionData ? formDataJson : formData,\n          });\n\n          if (\n            response &&\n            (validateStatus\n              ? !validateStatus(response.status)\n              : response.status < 200 || response.status >= 300)\n          ) {\n            hasError = true;\n            onError && onError({ response });\n            type = String(response.status);\n          } else {\n            onSuccess && onSuccess({ response });\n          }\n        } catch (error: unknown) {\n          hasError = true;\n          onError && onError({ error });\n        }\n      }\n    })(event);\n\n    if (hasError && props.control) {\n      props.control._subjects.state.next({\n        isSubmitSuccessful: false,\n      });\n      props.control.setError('root.server', {\n        type,\n      });\n    }\n  };\n\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  return render ? (\n    <>\n      {render({\n        submit,\n      })}\n    </>\n  ) : (\n    <form\n      noValidate={mounted}\n      action={action}\n      method={method}\n      encType={encType}\n      onSubmit={submit}\n      {...rest}\n    >\n      {children}\n    </form>\n  );\n}\n\nexport { Form };\n", "import type {\n  InternalFieldErrors,\n  InternalFieldName,\n  ValidateResult,\n} from '../types';\n\nexport default (\n  name: InternalFieldName,\n  validateAllFieldCriteria: boolean,\n  errors: InternalFieldErrors,\n  type: string,\n  message: ValidateResult,\n) =>\n  validateAllFieldCriteria\n    ? {\n        ...errors[name],\n        types: {\n          ...(errors[name] && errors[name]!.types ? errors[name]!.types : {}),\n          [type]: message || true,\n        },\n      }\n    : {};\n", "export default <T>(value: T) => (Array.isArray(value) ? value : [value]);\n", "import type { Noop } from '../types';\n\nexport type Observer<T> = {\n  next: (value: T) => void;\n};\n\nexport type Subscription = {\n  unsubscribe: Noop;\n};\n\nexport type Subject<T> = {\n  readonly observers: Observer<T>[];\n  subscribe: (value: Observer<T>) => Subscription;\n  unsubscribe: Noop;\n} & Observer<T>;\n\nexport default <T>(): Subject<T> => {\n  let _observers: Observer<T>[] = [];\n\n  const next = (value: T) => {\n    for (const observer of _observers) {\n      observer.next && observer.next(value);\n    }\n  };\n\n  const subscribe = (observer: Observer<T>): Subscription => {\n    _observers.push(observer);\n    return {\n      unsubscribe: () => {\n        _observers = _observers.filter((o) => o !== observer);\n      },\n    };\n  };\n\n  const unsubscribe = () => {\n    _observers = [];\n  };\n\n  return {\n    get observers() {\n      return _observers;\n    },\n    next,\n    subscribe,\n    unsubscribe,\n  };\n};\n", "import type { EmptyObject } from '../types';\n\nimport isObject from './isObject';\n\nexport default (value: unknown): value is EmptyObject =>\n  isObject(value) && !Object.keys(value).length;\n", "import type { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'file';\n", "export default (value: unknown): value is Function =>\n  typeof value === 'function';\n", "import isWeb from './isWeb';\n\nexport default (value: unknown): value is HTMLElement => {\n  if (!isWeb) {\n    return false;\n  }\n\n  const owner = value ? ((value as HTMLElement).ownerDocument as Document) : 0;\n  return (\n    value instanceof\n    (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement)\n  );\n};\n", "import type { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLSelectElement =>\n  element.type === `select-multiple`;\n", "import type { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'radio';\n", "import type { Ref } from '../types';\n\nimport isHTMLElement from './isHTMLElement';\n\nexport default (ref: Ref) => isHTMLElement(ref) && ref.isConnected;\n", "import isEmptyObject from './isEmptyObject';\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\nimport stringToPath from './stringToPath';\n\nfunction baseGet(object: any, updatePath: (string | number)[]) {\n  const length = updatePath.slice(0, -1).length;\n  let index = 0;\n\n  while (index < length) {\n    object = isUndefined(object) ? index++ : object[updatePath[index++]];\n  }\n\n  return object;\n}\n\nfunction isEmptyArray(obj: unknown[]) {\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport default function unset(object: any, path: string | (string | number)[]) {\n  const paths = Array.isArray(path)\n    ? path\n    : isKey(path)\n      ? [path]\n      : stringToPath(path);\n\n  const childObject = paths.length === 1 ? object : baseGet(object, paths);\n\n  const index = paths.length - 1;\n  const key = paths[index];\n\n  if (childObject) {\n    delete childObject[key];\n  }\n\n  if (\n    index !== 0 &&\n    ((isObject(childObject) && isEmptyObject(childObject)) ||\n      (Array.isArray(childObject) && isEmptyArray(childObject)))\n  ) {\n    unset(object, paths.slice(0, -1));\n  }\n\n  return object;\n}\n", "import isFunction from './isFunction';\n\nexport default <T>(data: T): boolean => {\n  for (const key in data) {\n    if (isFunction(data[key])) {\n      return true;\n    }\n  }\n  return false;\n};\n", "import deepEqual from '../utils/deepEqual';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isPrimitive from '../utils/isPrimitive';\nimport isUndefined from '../utils/isUndefined';\nimport objectHasFunction from '../utils/objectHasFunction';\n\nfunction markFieldsDirty<T>(data: T, fields: Record<string, any> = {}) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        fields[key] = Array.isArray(data[key]) ? [] : {};\n        markFieldsDirty(data[key], fields[key]);\n      } else if (!isNullOrUndefined(data[key])) {\n        fields[key] = true;\n      }\n    }\n  }\n\n  return fields;\n}\n\nfunction getDirtyFieldsFromDefaultValues<T>(\n  data: T,\n  formValues: T,\n  dirtyFieldsFromValues: Record<\n    Extract<keyof T, string>,\n    ReturnType<typeof markFieldsDirty> | boolean\n  >,\n) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        if (\n          isUndefined(formValues) ||\n          isPrimitive(dirtyFieldsFromValues[key])\n        ) {\n          dirtyFieldsFromValues[key] = Array.isArray(data[key])\n            ? markFieldsDirty(data[key], [])\n            : { ...markFieldsDirty(data[key]) };\n        } else {\n          getDirtyFieldsFromDefaultValues(\n            data[key],\n            isNullOrUndefined(formValues) ? {} : formValues[key],\n            dirtyFieldsFromValues[key],\n          );\n        }\n      } else {\n        dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n      }\n    }\n  }\n\n  return dirtyFieldsFromValues;\n}\n\nexport default <T>(defaultValues: T, formValues: T) =>\n  getDirtyFieldsFromDefaultValues(\n    defaultValues,\n    formValues,\n    markFieldsDirty(formValues),\n  );\n", "import isUndefined from '../utils/isUndefined';\n\ntype CheckboxFieldResult = {\n  isValid: boolean;\n  value: string | string[] | boolean | undefined;\n};\n\nconst defaultResult: CheckboxFieldResult = {\n  value: false,\n  isValid: false,\n};\n\nconst validResult = { value: true, isValid: true };\n\nexport default (options?: HTMLInputElement[]): CheckboxFieldResult => {\n  if (Array.isArray(options)) {\n    if (options.length > 1) {\n      const values = options\n        .filter((option) => option && option.checked && !option.disabled)\n        .map((option) => option.value);\n      return { value: values, isValid: !!values.length };\n    }\n\n    return options[0].checked && !options[0].disabled\n      ? // @ts-expect-error expected to work in the browser\n        options[0].attributes && !isUndefined(options[0].attributes.value)\n        ? isUndefined(options[0].value) || options[0].value === ''\n          ? validResult\n          : { value: options[0].value, isValid: true }\n        : validResult\n      : defaultResult;\n  }\n\n  return defaultResult;\n};\n", "import type { Field, NativeFieldValue } from '../types';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends NativeFieldValue>(\n  value: T,\n  { valueAsNumber, valueAsDate, setValueAs }: Field['_f'],\n) =>\n  isUndefined(value)\n    ? value\n    : valueAsNumber\n      ? value === ''\n        ? NaN\n        : value\n          ? +value\n          : value\n      : valueAsDate && isString(value)\n        ? new Date(value)\n        : setValueAs\n          ? setValueAs(value)\n          : value;\n", "type RadioFieldResult = {\n  isValid: boolean;\n  value: number | string | null;\n};\n\nconst defaultReturn: RadioFieldResult = {\n  isValid: false,\n  value: null,\n};\n\nexport default (options?: HTMLInputElement[]): RadioFieldResult =>\n  Array.isArray(options)\n    ? options.reduce(\n        (previous, option): RadioFieldResult =>\n          option && option.checked && !option.disabled\n            ? {\n                isValid: true,\n                value: option.value,\n              }\n            : previous,\n        defaultReturn,\n      )\n    : defaultReturn;\n", "import type { Field } from '../types';\nimport isCheckBox from '../utils/isCheckBoxInput';\nimport isFileInput from '../utils/isFileInput';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isRadioInput from '../utils/isRadioInput';\nimport isUndefined from '../utils/isUndefined';\n\nimport getCheckboxValue from './getCheckboxValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getRadioValue from './getRadioValue';\n\nexport default function getFieldValue(_f: Field['_f']) {\n  const ref = _f.ref;\n\n  if (isFileInput(ref)) {\n    return ref.files;\n  }\n\n  if (isRadioInput(ref)) {\n    return getRadioValue(_f.refs).value;\n  }\n\n  if (isMultipleSelect(ref)) {\n    return [...ref.selectedOptions].map(({ value }) => value);\n  }\n\n  if (isCheckBox(ref)) {\n    return getCheckboxValue(_f.refs).value;\n  }\n\n  return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\n", "import type {\n  CriteriaMode,\n  Field,\n  FieldName,\n  FieldRefs,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport { get } from '../utils';\nimport set from '../utils/set';\n\nexport default <TFieldValues extends FieldValues>(\n  fieldsNames: Set<InternalFieldName> | InternalFieldName[],\n  _fields: FieldRefs,\n  criteriaMode?: CriteriaMode,\n  shouldUseNativeValidation?: boolean | undefined,\n) => {\n  const fields: Record<InternalFieldName, Field['_f']> = {};\n\n  for (const name of fieldsNames) {\n    const field: Field = get(_fields, name);\n\n    field && set(fields, name, field._f);\n  }\n\n  return {\n    criteriaMode,\n    names: [...fieldsNames] as FieldName<TFieldValues>[],\n    fields,\n    shouldUseNativeValidation,\n  };\n};\n", "export default (value: unknown): value is RegExp => value instanceof RegExp;\n", "import type {\n  ValidationRule,\n  ValidationValue,\n  ValidationValueMessage,\n} from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends ValidationValue>(\n  rule?: ValidationRule<T> | ValidationValueMessage<T>,\n) =>\n  isUndefined(rule)\n    ? rule\n    : isRegex(rule)\n      ? rule.source\n      : isObject(rule)\n        ? isRegex(rule.value)\n          ? rule.value.source\n          : rule.value\n        : rule;\n", "import { VALIDATION_MODE } from '../constants';\nimport type { Mode, ValidationModeFlags } from '../types';\n\nexport default (mode?: Mode): ValidationModeFlags => ({\n  isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n  isOnBlur: mode === VALIDATION_MODE.onBlur,\n  isOnChange: mode === VALIDATION_MODE.onChange,\n  isOnAll: mode === VALIDATION_MODE.all,\n  isOnTouch: mode === VALIDATION_MODE.onTouched,\n});\n", "import type { Field, Validate } from '../types';\nimport isFunction from '../utils/isFunction';\nimport isObject from '../utils/isObject';\n\nconst ASYNC_FUNCTION = 'AsyncFunction';\n\nexport default (fieldReference: Field['_f']) =>\n  !!fieldReference &&\n  !!fieldReference.validate &&\n  !!(\n    (isFunction(fieldReference.validate) &&\n      fieldReference.validate.constructor.name === ASYNC_FUNCTION) ||\n    (isObject(fieldReference.validate) &&\n      Object.values(fieldReference.validate).find(\n        (validateFunction: Validate<unknown, unknown>) =>\n          validateFunction.constructor.name === ASYNC_FUNCTION,\n      ))\n  );\n", "import type { InternalFieldName, Names } from '../types';\n\nexport default (\n  name: InternalFieldName,\n  _names: Names,\n  isBlurEvent?: boolean,\n) =>\n  !isBlurEvent &&\n  (_names.watchAll ||\n    _names.watch.has(name) ||\n    [..._names.watch].some(\n      (watchName) =>\n        name.startsWith(watchName) &&\n        /^\\.\\w+/.test(name.slice(watchName.length)),\n    ));\n", "import type { FieldRefs, InternalFieldName, Ref } from '../types';\nimport { get } from '../utils';\nimport isObject from '../utils/isObject';\n\nconst iterateFieldsByAction = (\n  fields: FieldRefs,\n  action: (ref: Ref, name: string) => 1 | undefined | void,\n  fieldsNames?: Set<InternalFieldName> | InternalFieldName[] | 0,\n  abortEarly?: boolean,\n) => {\n  for (const key of fieldsNames || Object.keys(fields)) {\n    const field = get(fields, key);\n\n    if (field) {\n      const { _f, ...currentField } = field;\n\n      if (_f) {\n        if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n          return true;\n        } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n          return true;\n        } else {\n          if (iterateFieldsByAction(currentField, action)) {\n            break;\n          }\n        }\n      } else if (isObject(currentField)) {\n        if (iterateFieldsByAction(currentField as FieldRefs, action)) {\n          break;\n        }\n      }\n    }\n  }\n  return;\n};\nexport default iterateFieldsByAction;\n", "import type { FieldError, FieldErrors, FieldValues } from '../types';\nimport get from '../utils/get';\nimport isKey from '../utils/isKey';\n\nexport default function schemaErrorLookup<T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  _fields: FieldValues,\n  name: string,\n): {\n  error?: FieldError;\n  name: string;\n} {\n  const error = get(errors, name);\n\n  if (error || isKey(name)) {\n    return {\n      error,\n      name,\n    };\n  }\n\n  const names = name.split('.');\n\n  while (names.length) {\n    const fieldName = names.join('.');\n    const field = get(_fields, fieldName);\n    const foundError = get(errors, fieldName);\n\n    if (field && !Array.isArray(field) && name !== fieldName) {\n      return { name };\n    }\n\n    if (foundError && foundError.type) {\n      return {\n        name: fieldName,\n        error: foundError,\n      };\n    }\n\n    if (foundError && foundError.root && foundError.root.type) {\n      return {\n        name: `${fieldName}.root`,\n        error: foundError.root,\n      };\n    }\n\n    names.pop();\n  }\n\n  return {\n    name,\n  };\n}\n", "import { VALIDATION_MODE } from '../constants';\nimport type {\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  ReadFormState,\n} from '../types';\nimport isEmptyObject from '../utils/isEmptyObject';\n\nexport default <T extends FieldValues, K extends ReadFormState>(\n  formStateData: Partial<FormState<T>> & {\n    name?: InternalFieldName;\n    values?: T;\n  },\n  _proxyFormState: K,\n  updateFormState: (formState: Partial<FormState<T>>) => void,\n  isRoot?: boolean,\n) => {\n  updateFormState(formStateData);\n  const { name, ...formState } = formStateData;\n\n  return (\n    isEmptyObject(formState) ||\n    Object.keys(formState).length >= Object.keys(_proxyFormState).length ||\n    Object.keys(formState).find(\n      (key) =>\n        _proxyFormState[key as keyof ReadFormState] ===\n        (!isRoot || VALIDATION_MODE.all),\n    )\n  );\n};\n", "import type {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport get from '../utils/get';\nimport set from '../utils/set';\n\nexport default <T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  error: Partial<Record<string, FieldError>>,\n  name: InternalFieldName,\n): FieldErrors<T> => {\n  const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n  set(fieldArrayErrors, 'root', error[name]);\n  set(errors, name, fieldArrayErrors);\n  return errors;\n};\n", "import type { Message } from '../types';\nimport isString from '../utils/isString';\n\nexport default (value: unknown): value is Message => isString(value);\n", "import type { Field<PERSON>rror, Ref, ValidateResult } from '../types';\nimport isBoolean from '../utils/isBoolean';\nimport isMessage from '../utils/isMessage';\n\nexport default function getValidateError(\n  result: ValidateResult,\n  ref: Ref,\n  type = 'validate',\n): FieldError | void {\n  if (\n    isMessage(result) ||\n    (Array.isArray(result) && result.every(isMessage)) ||\n    (isBoolean(result) && !result)\n  ) {\n    return {\n      type,\n      message: isMessage(result) ? result : '',\n      ref,\n    };\n  }\n}\n", "import type { ValidationRule } from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\n\nexport default (validationData?: ValidationRule) =>\n  isObject(validationData) && !isRegex(validationData)\n    ? validationData\n    : {\n        value: validationData,\n        message: '',\n      };\n", "import { INPUT_VALIDATION_RULES } from '../constants';\nimport type {\n  Field,\n  FieldError,\n  FieldValues,\n  InternalFieldErrors,\n  InternalNameSet,\n  MaxType,\n  Message,\n  MinType,\n  NativeFieldValue,\n} from '../types';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMessage from '../utils/isMessage';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioInput from '../utils/isRadioInput';\nimport isRegex from '../utils/isRegex';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nimport appendErrors from './appendErrors';\nimport getCheckboxValue from './getCheckboxValue';\nimport getRadioValue from './getRadioValue';\nimport getValidateError from './getValidateError';\nimport getValueAndMessage from './getValueAndMessage';\n\nexport default async <T extends FieldValues>(\n  field: Field,\n  disabledFieldNames: InternalNameSet,\n  formValues: T,\n  validateAllFieldCriteria: boolean,\n  shouldUseNativeValidation?: boolean,\n  isFieldArray?: boolean,\n): Promise<InternalFieldErrors> => {\n  const {\n    ref,\n    refs,\n    required,\n    maxLength,\n    minLength,\n    min,\n    max,\n    pattern,\n    validate,\n    name,\n    valueAsNumber,\n    mount,\n  } = field._f;\n  const inputValue: NativeFieldValue = get(formValues, name);\n  if (!mount || disabledFieldNames.has(name)) {\n    return {};\n  }\n  const inputRef: HTMLInputElement = refs ? refs[0] : (ref as HTMLInputElement);\n  const setCustomValidity = (message?: string | boolean) => {\n    if (shouldUseNativeValidation && inputRef.reportValidity) {\n      inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n      inputRef.reportValidity();\n    }\n  };\n  const error: InternalFieldErrors = {};\n  const isRadio = isRadioInput(ref);\n  const isCheckBox = isCheckBoxInput(ref);\n  const isRadioOrCheckbox = isRadio || isCheckBox;\n  const isEmpty =\n    ((valueAsNumber || isFileInput(ref)) &&\n      isUndefined(ref.value) &&\n      isUndefined(inputValue)) ||\n    (isHTMLElement(ref) && ref.value === '') ||\n    inputValue === '' ||\n    (Array.isArray(inputValue) && !inputValue.length);\n  const appendErrorsCurry = appendErrors.bind(\n    null,\n    name,\n    validateAllFieldCriteria,\n    error,\n  );\n  const getMinMaxMessage = (\n    exceedMax: boolean,\n    maxLengthMessage: Message,\n    minLengthMessage: Message,\n    maxType: MaxType = INPUT_VALIDATION_RULES.maxLength,\n    minType: MinType = INPUT_VALIDATION_RULES.minLength,\n  ) => {\n    const message = exceedMax ? maxLengthMessage : minLengthMessage;\n    error[name] = {\n      type: exceedMax ? maxType : minType,\n      message,\n      ref,\n      ...appendErrorsCurry(exceedMax ? maxType : minType, message),\n    };\n  };\n\n  if (\n    isFieldArray\n      ? !Array.isArray(inputValue) || !inputValue.length\n      : required &&\n        ((!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue))) ||\n          (isBoolean(inputValue) && !inputValue) ||\n          (isCheckBox && !getCheckboxValue(refs).isValid) ||\n          (isRadio && !getRadioValue(refs).isValid))\n  ) {\n    const { value, message } = isMessage(required)\n      ? { value: !!required, message: required }\n      : getValueAndMessage(required);\n\n    if (value) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.required,\n        message,\n        ref: inputRef,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n    let exceedMax;\n    let exceedMin;\n    const maxOutput = getValueAndMessage(max);\n    const minOutput = getValueAndMessage(min);\n\n    if (!isNullOrUndefined(inputValue) && !isNaN(inputValue as number)) {\n      const valueNumber =\n        (ref as HTMLInputElement).valueAsNumber ||\n        (inputValue ? +inputValue : inputValue);\n      if (!isNullOrUndefined(maxOutput.value)) {\n        exceedMax = valueNumber > maxOutput.value;\n      }\n      if (!isNullOrUndefined(minOutput.value)) {\n        exceedMin = valueNumber < minOutput.value;\n      }\n    } else {\n      const valueDate =\n        (ref as HTMLInputElement).valueAsDate || new Date(inputValue as string);\n      const convertTimeToDate = (time: unknown) =>\n        new Date(new Date().toDateString() + ' ' + time);\n      const isTime = ref.type == 'time';\n      const isWeek = ref.type == 'week';\n\n      if (isString(maxOutput.value) && inputValue) {\n        exceedMax = isTime\n          ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value)\n          : isWeek\n            ? inputValue > maxOutput.value\n            : valueDate > new Date(maxOutput.value);\n      }\n\n      if (isString(minOutput.value) && inputValue) {\n        exceedMin = isTime\n          ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value)\n          : isWeek\n            ? inputValue < minOutput.value\n            : valueDate < new Date(minOutput.value);\n      }\n    }\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        !!exceedMax,\n        maxOutput.message,\n        minOutput.message,\n        INPUT_VALIDATION_RULES.max,\n        INPUT_VALIDATION_RULES.min,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (\n    (maxLength || minLength) &&\n    !isEmpty &&\n    (isString(inputValue) || (isFieldArray && Array.isArray(inputValue)))\n  ) {\n    const maxLengthOutput = getValueAndMessage(maxLength);\n    const minLengthOutput = getValueAndMessage(minLength);\n    const exceedMax =\n      !isNullOrUndefined(maxLengthOutput.value) &&\n      inputValue.length > +maxLengthOutput.value;\n    const exceedMin =\n      !isNullOrUndefined(minLengthOutput.value) &&\n      inputValue.length < +minLengthOutput.value;\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        exceedMax,\n        maxLengthOutput.message,\n        minLengthOutput.message,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (pattern && !isEmpty && isString(inputValue)) {\n    const { value: patternValue, message } = getValueAndMessage(pattern);\n\n    if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.pattern,\n        message,\n        ref,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (validate) {\n    if (isFunction(validate)) {\n      const result = await validate(inputValue, formValues);\n      const validateError = getValidateError(result, inputRef);\n\n      if (validateError) {\n        error[name] = {\n          ...validateError,\n          ...appendErrorsCurry(\n            INPUT_VALIDATION_RULES.validate,\n            validateError.message,\n          ),\n        };\n        if (!validateAllFieldCriteria) {\n          setCustomValidity(validateError.message);\n          return error;\n        }\n      }\n    } else if (isObject(validate)) {\n      let validationResult = {} as FieldError;\n\n      for (const key in validate) {\n        if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n          break;\n        }\n\n        const validateError = getValidateError(\n          await validate[key](inputValue, formValues),\n          inputRef,\n          key,\n        );\n\n        if (validateError) {\n          validationResult = {\n            ...validateError,\n            ...appendErrorsCurry(key, validateError.message),\n          };\n\n          setCustomValidity(validateError.message);\n\n          if (validateAllFieldCriteria) {\n            error[name] = validationResult;\n          }\n        }\n      }\n\n      if (!isEmptyObject(validationResult)) {\n        error[name] = {\n          ref: inputRef,\n          ...validationResult,\n        };\n        if (!validateAllFieldCriteria) {\n          return error;\n        }\n      }\n    }\n  }\n\n  setCustomValidity(true);\n  return error;\n};\n", "import { EVENTS, VALIDATION_MODE } from '../constants';\nimport type {\n  BatchField<PERSON>rrayUpdate,\n  ChangeHandler,\n  Control,\n  DeepPartial,\n  DelayCallback,\n  EventType,\n  Field,\n  FieldError,\n  FieldErrors,\n  FieldNamesMarkedBoolean,\n  FieldPath,\n  FieldRefs,\n  FieldValues,\n  FormState,\n  FromSubscribe,\n  GetIsDirty,\n  InternalFieldName,\n  Names,\n  Path,\n  ReadFormState,\n  Ref,\n  SetFieldValue,\n  SetValueConfig,\n  Subjects,\n  UseFormClearErrors,\n  UseFormGetFieldState,\n  UseFormGetValues,\n  UseFormHandleSubmit,\n  UseFormProps,\n  UseFormRegister,\n  UseFormReset,\n  UseFormResetField,\n  UseFormReturn,\n  UseFormSetError,\n  UseFormSetFocus,\n  UseFormSetValue,\n  UseFormSubscribe,\n  UseFormTrigger,\n  UseFormUnregister,\n  UseFormWatch,\n  WatchInternal,\n  WatchObserver,\n} from '../types';\nimport cloneObject from '../utils/cloneObject';\nimport compact from '../utils/compact';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport createSubject from '../utils/createSubject';\nimport deepEqual from '../utils/deepEqual';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isDateObject from '../utils/isDateObject';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioOrCheckbox from '../utils/isRadioOrCheckbox';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\nimport isWeb from '../utils/isWeb';\nimport live from '../utils/live';\nimport set from '../utils/set';\nimport unset from '../utils/unset';\n\nimport generateWatchOutput from './generateWatchOutput';\nimport getDirtyFields from './getDirtyFields';\nimport getEventValue from './getEventValue';\nimport getFieldValue from './getFieldValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getResolverOptions from './getResolverOptions';\nimport getRuleValue from './getRuleValue';\nimport getValidationModes from './getValidationModes';\nimport hasPromiseValidation from './hasPromiseValidation';\nimport hasValidation from './hasValidation';\nimport isNameInFieldArray from './isNameInFieldArray';\nimport isWatched from './isWatched';\nimport iterateFieldsByAction from './iterateFieldsByAction';\nimport schemaErrorLookup from './schemaErrorLookup';\nimport shouldRenderFormState from './shouldRenderFormState';\nimport shouldSubscribeByName from './shouldSubscribeByName';\nimport skipValidation from './skipValidation';\nimport unsetEmptyArray from './unsetEmptyArray';\nimport updateFieldArrayRootError from './updateFieldArrayRootError';\nimport validateField from './validateField';\n\nconst defaultOptions = {\n  mode: VALIDATION_MODE.onSubmit,\n  reValidateMode: VALIDATION_MODE.onChange,\n  shouldFocusError: true,\n} as const;\n\nexport function createFormControl<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFormProps<TFieldValues, TContext, TTransformedValues> = {},\n): Omit<\n  UseFormReturn<TFieldValues, TContext, TTransformedValues>,\n  'formState'\n> & {\n  formControl: Omit<\n    UseFormReturn<TFieldValues, TContext, TTransformedValues>,\n    'formState'\n  >;\n} {\n  let _options = {\n    ...defaultOptions,\n    ...props,\n  };\n  let _formState: FormState<TFieldValues> = {\n    submitCount: 0,\n    isDirty: false,\n    isReady: false,\n    isLoading: isFunction(_options.defaultValues),\n    isValidating: false,\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    touchedFields: {},\n    dirtyFields: {},\n    validatingFields: {},\n    errors: _options.errors || {},\n    disabled: _options.disabled || false,\n  };\n  let _fields: FieldRefs = {};\n  let _defaultValues =\n    isObject(_options.defaultValues) || isObject(_options.values)\n      ? cloneObject(_options.defaultValues || _options.values) || {}\n      : {};\n  let _formValues = _options.shouldUnregister\n    ? ({} as TFieldValues)\n    : (cloneObject(_defaultValues) as TFieldValues);\n  let _state = {\n    action: false,\n    mount: false,\n    watch: false,\n  };\n  let _names: Names = {\n    mount: new Set(),\n    disabled: new Set(),\n    unMount: new Set(),\n    array: new Set(),\n    watch: new Set(),\n  };\n  let delayErrorCallback: DelayCallback | null;\n  let timer = 0;\n  const _proxyFormState: ReadFormState = {\n    isDirty: false,\n    dirtyFields: false,\n    validatingFields: false,\n    touchedFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  };\n  let _proxySubscribeFormState = {\n    ..._proxyFormState,\n  };\n  const _subjects: Subjects<TFieldValues> = {\n    array: createSubject(),\n    state: createSubject(),\n  };\n\n  const shouldDisplayAllAssociatedErrors =\n    _options.criteriaMode === VALIDATION_MODE.all;\n\n  const debounce =\n    <T extends Function>(callback: T) =>\n    (wait: number) => {\n      clearTimeout(timer);\n      timer = setTimeout(callback, wait);\n    };\n\n  const _setValid = async (shouldUpdateValid?: boolean) => {\n    if (\n      !_options.disabled &&\n      (_proxyFormState.isValid ||\n        _proxySubscribeFormState.isValid ||\n        shouldUpdateValid)\n    ) {\n      const isValid = _options.resolver\n        ? isEmptyObject((await _runSchema()).errors)\n        : await executeBuiltInValidation(_fields, true);\n\n      if (isValid !== _formState.isValid) {\n        _subjects.state.next({\n          isValid,\n        });\n      }\n    }\n  };\n\n  const _updateIsValidating = (names?: string[], isValidating?: boolean) => {\n    if (\n      !_options.disabled &&\n      (_proxyFormState.isValidating ||\n        _proxyFormState.validatingFields ||\n        _proxySubscribeFormState.isValidating ||\n        _proxySubscribeFormState.validatingFields)\n    ) {\n      (names || Array.from(_names.mount)).forEach((name) => {\n        if (name) {\n          isValidating\n            ? set(_formState.validatingFields, name, isValidating)\n            : unset(_formState.validatingFields, name);\n        }\n      });\n\n      _subjects.state.next({\n        validatingFields: _formState.validatingFields,\n        isValidating: !isEmptyObject(_formState.validatingFields),\n      });\n    }\n  };\n\n  const _setFieldArray: BatchFieldArrayUpdate = (\n    name,\n    values = [],\n    method,\n    args,\n    shouldSetValues = true,\n    shouldUpdateFieldsAndState = true,\n  ) => {\n    if (args && method && !_options.disabled) {\n      _state.action = true;\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n        const fieldValues = method(get(_fields, name), args.argA, args.argB);\n        shouldSetValues && set(_fields, name, fieldValues);\n      }\n\n      if (\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.errors, name))\n      ) {\n        const errors = method(\n          get(_formState.errors, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.errors, name, errors);\n        unsetEmptyArray(_formState.errors, name);\n      }\n\n      if (\n        (_proxyFormState.touchedFields ||\n          _proxySubscribeFormState.touchedFields) &&\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.touchedFields, name))\n      ) {\n        const touchedFields = method(\n          get(_formState.touchedFields, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n      }\n\n      if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n        _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n      }\n\n      _subjects.state.next({\n        name,\n        isDirty: _getDirty(name, values),\n        dirtyFields: _formState.dirtyFields,\n        errors: _formState.errors,\n        isValid: _formState.isValid,\n      });\n    } else {\n      set(_formValues, name, values);\n    }\n  };\n\n  const updateErrors = (name: InternalFieldName, error: FieldError) => {\n    set(_formState.errors, name, error);\n    _subjects.state.next({\n      errors: _formState.errors,\n    });\n  };\n\n  const _setErrors = (errors: FieldErrors<TFieldValues>) => {\n    _formState.errors = errors;\n    _subjects.state.next({\n      errors: _formState.errors,\n      isValid: false,\n    });\n  };\n\n  const updateValidAndValue = (\n    name: InternalFieldName,\n    shouldSkipSetValueAs: boolean,\n    value?: unknown,\n    ref?: Ref,\n  ) => {\n    const field: Field = get(_fields, name);\n\n    if (field) {\n      const defaultValue = get(\n        _formValues,\n        name,\n        isUndefined(value) ? get(_defaultValues, name) : value,\n      );\n\n      isUndefined(defaultValue) ||\n      (ref && (ref as HTMLInputElement).defaultChecked) ||\n      shouldSkipSetValueAs\n        ? set(\n            _formValues,\n            name,\n            shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f),\n          )\n        : setFieldValue(name, defaultValue);\n\n      _state.mount && _setValid();\n    }\n  };\n\n  const updateTouchAndDirty = (\n    name: InternalFieldName,\n    fieldValue: unknown,\n    isBlurEvent?: boolean,\n    shouldDirty?: boolean,\n    shouldRender?: boolean,\n  ): Partial<\n    Pick<FormState<TFieldValues>, 'dirtyFields' | 'isDirty' | 'touchedFields'>\n  > => {\n    let shouldUpdateField = false;\n    let isPreviousDirty = false;\n    const output: Partial<FormState<TFieldValues>> & { name: string } = {\n      name,\n    };\n\n    if (!_options.disabled) {\n      if (!isBlurEvent || shouldDirty) {\n        if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n          isPreviousDirty = _formState.isDirty;\n          _formState.isDirty = output.isDirty = _getDirty();\n          shouldUpdateField = isPreviousDirty !== output.isDirty;\n        }\n\n        const isCurrentFieldPristine = deepEqual(\n          get(_defaultValues, name),\n          fieldValue,\n        );\n\n        isPreviousDirty = !!get(_formState.dirtyFields, name);\n        isCurrentFieldPristine\n          ? unset(_formState.dirtyFields, name)\n          : set(_formState.dirtyFields, name, true);\n        output.dirtyFields = _formState.dirtyFields;\n        shouldUpdateField =\n          shouldUpdateField ||\n          ((_proxyFormState.dirtyFields ||\n            _proxySubscribeFormState.dirtyFields) &&\n            isPreviousDirty !== !isCurrentFieldPristine);\n      }\n\n      if (isBlurEvent) {\n        const isPreviousFieldTouched = get(_formState.touchedFields, name);\n\n        if (!isPreviousFieldTouched) {\n          set(_formState.touchedFields, name, isBlurEvent);\n          output.touchedFields = _formState.touchedFields;\n          shouldUpdateField =\n            shouldUpdateField ||\n            ((_proxyFormState.touchedFields ||\n              _proxySubscribeFormState.touchedFields) &&\n              isPreviousFieldTouched !== isBlurEvent);\n        }\n      }\n\n      shouldUpdateField && shouldRender && _subjects.state.next(output);\n    }\n\n    return shouldUpdateField ? output : {};\n  };\n\n  const shouldRenderByError = (\n    name: InternalFieldName,\n    isValid?: boolean,\n    error?: FieldError,\n    fieldState?: {\n      dirty?: FieldNamesMarkedBoolean<TFieldValues>;\n      isDirty?: boolean;\n      touched?: FieldNamesMarkedBoolean<TFieldValues>;\n    },\n  ) => {\n    const previousFieldError = get(_formState.errors, name);\n    const shouldUpdateValid =\n      (_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n      isBoolean(isValid) &&\n      _formState.isValid !== isValid;\n\n    if (_options.delayError && error) {\n      delayErrorCallback = debounce(() => updateErrors(name, error));\n      delayErrorCallback(_options.delayError);\n    } else {\n      clearTimeout(timer);\n      delayErrorCallback = null;\n      error\n        ? set(_formState.errors, name, error)\n        : unset(_formState.errors, name);\n    }\n\n    if (\n      (error ? !deepEqual(previousFieldError, error) : previousFieldError) ||\n      !isEmptyObject(fieldState) ||\n      shouldUpdateValid\n    ) {\n      const updatedFormState = {\n        ...fieldState,\n        ...(shouldUpdateValid && isBoolean(isValid) ? { isValid } : {}),\n        errors: _formState.errors,\n        name,\n      };\n\n      _formState = {\n        ..._formState,\n        ...updatedFormState,\n      };\n\n      _subjects.state.next(updatedFormState);\n    }\n  };\n\n  const _runSchema = async (name?: InternalFieldName[]) => {\n    _updateIsValidating(name, true);\n    const result = await _options.resolver!(\n      _formValues as TFieldValues,\n      _options.context,\n      getResolverOptions(\n        name || _names.mount,\n        _fields,\n        _options.criteriaMode,\n        _options.shouldUseNativeValidation,\n      ),\n    );\n    _updateIsValidating(name);\n    return result;\n  };\n\n  const executeSchemaAndUpdateState = async (names?: InternalFieldName[]) => {\n    const { errors } = await _runSchema(names);\n\n    if (names) {\n      for (const name of names) {\n        const error = get(errors, name);\n        error\n          ? set(_formState.errors, name, error)\n          : unset(_formState.errors, name);\n      }\n    } else {\n      _formState.errors = errors;\n    }\n\n    return errors;\n  };\n\n  const executeBuiltInValidation = async (\n    fields: FieldRefs,\n    shouldOnlyCheckValid?: boolean,\n    context: {\n      valid: boolean;\n    } = {\n      valid: true,\n    },\n  ) => {\n    for (const name in fields) {\n      const field = fields[name];\n\n      if (field) {\n        const { _f, ...fieldValue } = field as Field;\n\n        if (_f) {\n          const isFieldArrayRoot = _names.array.has(_f.name);\n          const isPromiseFunction =\n            field._f && hasPromiseValidation((field as Field)._f);\n\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name], true);\n          }\n\n          const fieldError = await validateField(\n            field as Field,\n            _names.disabled,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation && !shouldOnlyCheckValid,\n            isFieldArrayRoot,\n          );\n\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name]);\n          }\n\n          if (fieldError[_f.name]) {\n            context.valid = false;\n            if (shouldOnlyCheckValid) {\n              break;\n            }\n          }\n\n          !shouldOnlyCheckValid &&\n            (get(fieldError, _f.name)\n              ? isFieldArrayRoot\n                ? updateFieldArrayRootError(\n                    _formState.errors,\n                    fieldError,\n                    _f.name,\n                  )\n                : set(_formState.errors, _f.name, fieldError[_f.name])\n              : unset(_formState.errors, _f.name));\n        }\n\n        !isEmptyObject(fieldValue) &&\n          (await executeBuiltInValidation(\n            fieldValue,\n            shouldOnlyCheckValid,\n            context,\n          ));\n      }\n    }\n\n    return context.valid;\n  };\n\n  const _removeUnmounted = () => {\n    for (const name of _names.unMount) {\n      const field: Field = get(_fields, name);\n\n      field &&\n        (field._f.refs\n          ? field._f.refs.every((ref) => !live(ref))\n          : !live(field._f.ref)) &&\n        unregister(name as FieldPath<TFieldValues>);\n    }\n\n    _names.unMount = new Set();\n  };\n\n  const _getDirty: GetIsDirty = (name, data) =>\n    !_options.disabled &&\n    (name && data && set(_formValues, name, data),\n    !deepEqual(getValues(), _defaultValues));\n\n  const _getWatch: WatchInternal<TFieldValues> = (\n    names,\n    defaultValue,\n    isGlobal,\n  ) =>\n    generateWatchOutput(\n      names,\n      _names,\n      {\n        ...(_state.mount\n          ? _formValues\n          : isUndefined(defaultValue)\n            ? _defaultValues\n            : isString(names)\n              ? { [names]: defaultValue }\n              : defaultValue),\n      },\n      isGlobal,\n      defaultValue,\n    );\n\n  const _getFieldArray = <TFieldArrayValues>(\n    name: InternalFieldName,\n  ): Partial<TFieldArrayValues>[] =>\n    compact(\n      get(\n        _state.mount ? _formValues : _defaultValues,\n        name,\n        _options.shouldUnregister ? get(_defaultValues, name, []) : [],\n      ),\n    );\n\n  const setFieldValue = (\n    name: InternalFieldName,\n    value: SetFieldValue<TFieldValues>,\n    options: SetValueConfig = {},\n  ) => {\n    const field: Field = get(_fields, name);\n    let fieldValue: unknown = value;\n\n    if (field) {\n      const fieldReference = field._f;\n\n      if (fieldReference) {\n        !fieldReference.disabled &&\n          set(_formValues, name, getFieldValueAs(value, fieldReference));\n\n        fieldValue =\n          isHTMLElement(fieldReference.ref) && isNullOrUndefined(value)\n            ? ''\n            : value;\n\n        if (isMultipleSelect(fieldReference.ref)) {\n          [...fieldReference.ref.options].forEach(\n            (optionRef) =>\n              (optionRef.selected = (\n                fieldValue as InternalFieldName[]\n              ).includes(optionRef.value)),\n          );\n        } else if (fieldReference.refs) {\n          if (isCheckBoxInput(fieldReference.ref)) {\n            fieldReference.refs.forEach((checkboxRef) => {\n              if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {\n                if (Array.isArray(fieldValue)) {\n                  checkboxRef.checked = !!fieldValue.find(\n                    (data: string) => data === checkboxRef.value,\n                  );\n                } else {\n                  checkboxRef.checked =\n                    fieldValue === checkboxRef.value || !!fieldValue;\n                }\n              }\n            });\n          } else {\n            fieldReference.refs.forEach(\n              (radioRef: HTMLInputElement) =>\n                (radioRef.checked = radioRef.value === fieldValue),\n            );\n          }\n        } else if (isFileInput(fieldReference.ref)) {\n          fieldReference.ref.value = '';\n        } else {\n          fieldReference.ref.value = fieldValue;\n\n          if (!fieldReference.ref.type) {\n            _subjects.state.next({\n              name,\n              values: cloneObject(_formValues),\n            });\n          }\n        }\n      }\n    }\n\n    (options.shouldDirty || options.shouldTouch) &&\n      updateTouchAndDirty(\n        name,\n        fieldValue,\n        options.shouldTouch,\n        options.shouldDirty,\n        true,\n      );\n\n    options.shouldValidate && trigger(name as Path<TFieldValues>);\n  };\n\n  const setValues = <\n    T extends InternalFieldName,\n    K extends SetFieldValue<TFieldValues>,\n    U extends SetValueConfig,\n  >(\n    name: T,\n    value: K,\n    options: U,\n  ) => {\n    for (const fieldKey in value) {\n      if (!value.hasOwnProperty(fieldKey)) {\n        return;\n      }\n      const fieldValue = value[fieldKey];\n      const fieldName = name + '.' + fieldKey;\n      const field = get(_fields, fieldName);\n\n      (_names.array.has(name) ||\n        isObject(fieldValue) ||\n        (field && !field._f)) &&\n      !isDateObject(fieldValue)\n        ? setValues(fieldName, fieldValue, options)\n        : setFieldValue(fieldName, fieldValue, options);\n    }\n  };\n\n  const setValue: UseFormSetValue<TFieldValues> = (\n    name,\n    value,\n    options = {},\n  ) => {\n    const field = get(_fields, name);\n    const isFieldArray = _names.array.has(name);\n    const cloneValue = cloneObject(value);\n\n    set(_formValues, name, cloneValue);\n\n    if (isFieldArray) {\n      _subjects.array.next({\n        name,\n        values: cloneObject(_formValues),\n      });\n\n      if (\n        (_proxyFormState.isDirty ||\n          _proxyFormState.dirtyFields ||\n          _proxySubscribeFormState.isDirty ||\n          _proxySubscribeFormState.dirtyFields) &&\n        options.shouldDirty\n      ) {\n        _subjects.state.next({\n          name,\n          dirtyFields: getDirtyFields(_defaultValues, _formValues),\n          isDirty: _getDirty(name, cloneValue),\n        });\n      }\n    } else {\n      field && !field._f && !isNullOrUndefined(cloneValue)\n        ? setValues(name, cloneValue, options)\n        : setFieldValue(name, cloneValue, options);\n    }\n\n    isWatched(name, _names) && _subjects.state.next({ ..._formState, name });\n    _subjects.state.next({\n      name: _state.mount ? name : undefined,\n      values: cloneObject(_formValues),\n    });\n  };\n\n  const onChange: ChangeHandler = async (event) => {\n    _state.mount = true;\n    const target = event.target;\n    let name: string = target.name;\n    let isFieldValueUpdated = true;\n    const field: Field = get(_fields, name);\n    const _updateIsFieldValueUpdated = (fieldValue: unknown) => {\n      isFieldValueUpdated =\n        Number.isNaN(fieldValue) ||\n        (isDateObject(fieldValue) && isNaN(fieldValue.getTime())) ||\n        deepEqual(fieldValue, get(_formValues, name, fieldValue));\n    };\n    const validationModeBeforeSubmit = getValidationModes(_options.mode);\n    const validationModeAfterSubmit = getValidationModes(\n      _options.reValidateMode,\n    );\n\n    if (field) {\n      let error;\n      let isValid;\n      const fieldValue = target.type\n        ? getFieldValue(field._f)\n        : getEventValue(event);\n      const isBlurEvent =\n        event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n      const shouldSkipValidation =\n        (!hasValidation(field._f) &&\n          !_options.resolver &&\n          !get(_formState.errors, name) &&\n          !field._f.deps) ||\n        skipValidation(\n          isBlurEvent,\n          get(_formState.touchedFields, name),\n          _formState.isSubmitted,\n          validationModeAfterSubmit,\n          validationModeBeforeSubmit,\n        );\n      const watched = isWatched(name, _names, isBlurEvent);\n\n      set(_formValues, name, fieldValue);\n\n      if (isBlurEvent) {\n        field._f.onBlur && field._f.onBlur(event);\n        delayErrorCallback && delayErrorCallback(0);\n      } else if (field._f.onChange) {\n        field._f.onChange(event);\n      }\n\n      const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n\n      const shouldRender = !isEmptyObject(fieldState) || watched;\n\n      !isBlurEvent &&\n        _subjects.state.next({\n          name,\n          type: event.type,\n          values: cloneObject(_formValues),\n        });\n\n      if (shouldSkipValidation) {\n        if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n          if (_options.mode === 'onBlur') {\n            if (isBlurEvent) {\n              _setValid();\n            }\n          } else if (!isBlurEvent) {\n            _setValid();\n          }\n        }\n\n        return (\n          shouldRender &&\n          _subjects.state.next({ name, ...(watched ? {} : fieldState) })\n        );\n      }\n\n      !isBlurEvent && watched && _subjects.state.next({ ..._formState });\n\n      if (_options.resolver) {\n        const { errors } = await _runSchema([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          const previousErrorLookupResult = schemaErrorLookup(\n            _formState.errors,\n            _fields,\n            name,\n          );\n          const errorLookupResult = schemaErrorLookup(\n            errors,\n            _fields,\n            previousErrorLookupResult.name || name,\n          );\n\n          error = errorLookupResult.error;\n          name = errorLookupResult.name;\n\n          isValid = isEmptyObject(errors);\n        }\n      } else {\n        _updateIsValidating([name], true);\n        error = (\n          await validateField(\n            field,\n            _names.disabled,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation,\n          )\n        )[name];\n        _updateIsValidating([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          if (error) {\n            isValid = false;\n          } else if (\n            _proxyFormState.isValid ||\n            _proxySubscribeFormState.isValid\n          ) {\n            isValid = await executeBuiltInValidation(_fields, true);\n          }\n        }\n      }\n\n      if (isFieldValueUpdated) {\n        field._f.deps &&\n          trigger(\n            field._f.deps as\n              | FieldPath<TFieldValues>\n              | FieldPath<TFieldValues>[],\n          );\n        shouldRenderByError(name, isValid, error, fieldState);\n      }\n    }\n  };\n\n  const _focusInput = (ref: Ref, key: string) => {\n    if (get(_formState.errors, key) && ref.focus) {\n      ref.focus();\n      return 1;\n    }\n    return;\n  };\n\n  const trigger: UseFormTrigger<TFieldValues> = async (name, options = {}) => {\n    let isValid;\n    let validationResult;\n    const fieldNames = convertToArrayPayload(name) as InternalFieldName[];\n\n    if (_options.resolver) {\n      const errors = await executeSchemaAndUpdateState(\n        isUndefined(name) ? name : fieldNames,\n      );\n\n      isValid = isEmptyObject(errors);\n      validationResult = name\n        ? !fieldNames.some((name) => get(errors, name))\n        : isValid;\n    } else if (name) {\n      validationResult = (\n        await Promise.all(\n          fieldNames.map(async (fieldName) => {\n            const field = get(_fields, fieldName);\n            return await executeBuiltInValidation(\n              field && field._f ? { [fieldName]: field } : field,\n            );\n          }),\n        )\n      ).every(Boolean);\n      !(!validationResult && !_formState.isValid) && _setValid();\n    } else {\n      validationResult = isValid = await executeBuiltInValidation(_fields);\n    }\n\n    _subjects.state.next({\n      ...(!isString(name) ||\n      ((_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n        isValid !== _formState.isValid)\n        ? {}\n        : { name }),\n      ...(_options.resolver || !name ? { isValid } : {}),\n      errors: _formState.errors,\n    });\n\n    options.shouldFocus &&\n      !validationResult &&\n      iterateFieldsByAction(\n        _fields,\n        _focusInput,\n        name ? fieldNames : _names.mount,\n      );\n\n    return validationResult;\n  };\n\n  const getValues: UseFormGetValues<TFieldValues> = (\n    fieldNames?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>,\n  ) => {\n    const values = {\n      ...(_state.mount ? _formValues : _defaultValues),\n    };\n\n    return isUndefined(fieldNames)\n      ? values\n      : isString(fieldNames)\n        ? get(values, fieldNames)\n        : fieldNames.map((name) => get(values, name));\n  };\n\n  const getFieldState: UseFormGetFieldState<TFieldValues> = (\n    name,\n    formState,\n  ) => ({\n    invalid: !!get((formState || _formState).errors, name),\n    isDirty: !!get((formState || _formState).dirtyFields, name),\n    error: get((formState || _formState).errors, name),\n    isValidating: !!get(_formState.validatingFields, name),\n    isTouched: !!get((formState || _formState).touchedFields, name),\n  });\n\n  const clearErrors: UseFormClearErrors<TFieldValues> = (name) => {\n    name &&\n      convertToArrayPayload(name).forEach((inputName) =>\n        unset(_formState.errors, inputName),\n      );\n\n    _subjects.state.next({\n      errors: name ? _formState.errors : {},\n    });\n  };\n\n  const setError: UseFormSetError<TFieldValues> = (name, error, options) => {\n    const ref = (get(_fields, name, { _f: {} })._f || {}).ref;\n    const currentError = get(_formState.errors, name) || {};\n\n    // Don't override existing error messages elsewhere in the object tree.\n    const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n\n    set(_formState.errors, name, {\n      ...restOfErrorTree,\n      ...error,\n      ref,\n    });\n\n    _subjects.state.next({\n      name,\n      errors: _formState.errors,\n      isValid: false,\n    });\n\n    options && options.shouldFocus && ref && ref.focus && ref.focus();\n  };\n\n  const watch: UseFormWatch<TFieldValues> = (\n    name?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>\n      | WatchObserver<TFieldValues>,\n    defaultValue?: DeepPartial<TFieldValues>,\n  ) =>\n    isFunction(name)\n      ? _subjects.state.subscribe({\n          next: (payload) =>\n            'values' in payload &&\n            name(\n              _getWatch(undefined, defaultValue),\n              payload as {\n                name?: FieldPath<TFieldValues>;\n                type?: EventType;\n                value?: unknown;\n              },\n            ),\n        })\n      : _getWatch(\n          name as InternalFieldName | InternalFieldName[],\n          defaultValue,\n          true,\n        );\n\n  const _subscribe: FromSubscribe<TFieldValues> = (props) =>\n    _subjects.state.subscribe({\n      next: (\n        formState: Partial<FormState<TFieldValues>> & {\n          name?: InternalFieldName;\n          values?: TFieldValues | undefined;\n          type?: EventType;\n        },\n      ) => {\n        if (\n          shouldSubscribeByName(props.name, formState.name, props.exact) &&\n          shouldRenderFormState(\n            formState,\n            (props.formState as ReadFormState) || _proxyFormState,\n            _setFormState,\n            props.reRenderRoot,\n          )\n        ) {\n          props.callback({\n            values: { ..._formValues } as TFieldValues,\n            ..._formState,\n            ...formState,\n            defaultValues:\n              _defaultValues as FormState<TFieldValues>['defaultValues'],\n          });\n        }\n      },\n    }).unsubscribe;\n\n  const subscribe: UseFormSubscribe<TFieldValues> = (props) => {\n    _state.mount = true;\n    _proxySubscribeFormState = {\n      ..._proxySubscribeFormState,\n      ...props.formState,\n    };\n    return _subscribe({\n      ...props,\n      formState: _proxySubscribeFormState,\n    });\n  };\n\n  const unregister: UseFormUnregister<TFieldValues> = (name, options = {}) => {\n    for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n      _names.mount.delete(fieldName);\n      _names.array.delete(fieldName);\n\n      if (!options.keepValue) {\n        unset(_fields, fieldName);\n        unset(_formValues, fieldName);\n      }\n\n      !options.keepError && unset(_formState.errors, fieldName);\n      !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n      !options.keepTouched && unset(_formState.touchedFields, fieldName);\n      !options.keepIsValidating &&\n        unset(_formState.validatingFields, fieldName);\n      !_options.shouldUnregister &&\n        !options.keepDefaultValue &&\n        unset(_defaultValues, fieldName);\n    }\n\n    _subjects.state.next({\n      values: cloneObject(_formValues),\n    });\n\n    _subjects.state.next({\n      ..._formState,\n      ...(!options.keepDirty ? {} : { isDirty: _getDirty() }),\n    });\n\n    !options.keepIsValid && _setValid();\n  };\n\n  const _setDisabledField: Control<TFieldValues>['_setDisabledField'] = ({\n    disabled,\n    name,\n  }) => {\n    if (\n      (isBoolean(disabled) && _state.mount) ||\n      !!disabled ||\n      _names.disabled.has(name)\n    ) {\n      disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n    }\n  };\n\n  const register: UseFormRegister<TFieldValues> = (name, options = {}) => {\n    let field = get(_fields, name);\n    const disabledIsDefined =\n      isBoolean(options.disabled) || isBoolean(_options.disabled);\n\n    set(_fields, name, {\n      ...(field || {}),\n      _f: {\n        ...(field && field._f ? field._f : { ref: { name } }),\n        name,\n        mount: true,\n        ...options,\n      },\n    });\n    _names.mount.add(name);\n\n    if (field) {\n      _setDisabledField({\n        disabled: isBoolean(options.disabled)\n          ? options.disabled\n          : _options.disabled,\n        name,\n      });\n    } else {\n      updateValidAndValue(name, true, options.value);\n    }\n\n    return {\n      ...(disabledIsDefined\n        ? { disabled: options.disabled || _options.disabled }\n        : {}),\n      ...(_options.progressive\n        ? {\n            required: !!options.required,\n            min: getRuleValue(options.min),\n            max: getRuleValue(options.max),\n            minLength: getRuleValue<number>(options.minLength) as number,\n            maxLength: getRuleValue(options.maxLength) as number,\n            pattern: getRuleValue(options.pattern) as string,\n          }\n        : {}),\n      name,\n      onChange,\n      onBlur: onChange,\n      ref: (ref: HTMLInputElement | null): void => {\n        if (ref) {\n          register(name, options);\n          field = get(_fields, name);\n\n          const fieldRef = isUndefined(ref.value)\n            ? ref.querySelectorAll\n              ? (ref.querySelectorAll('input,select,textarea')[0] as Ref) || ref\n              : ref\n            : ref;\n          const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n          const refs = field._f.refs || [];\n\n          if (\n            radioOrCheckbox\n              ? refs.find((option: Ref) => option === fieldRef)\n              : fieldRef === field._f.ref\n          ) {\n            return;\n          }\n\n          set(_fields, name, {\n            _f: {\n              ...field._f,\n              ...(radioOrCheckbox\n                ? {\n                    refs: [\n                      ...refs.filter(live),\n                      fieldRef,\n                      ...(Array.isArray(get(_defaultValues, name)) ? [{}] : []),\n                    ],\n                    ref: { type: fieldRef.type, name },\n                  }\n                : { ref: fieldRef }),\n            },\n          });\n\n          updateValidAndValue(name, false, undefined, fieldRef);\n        } else {\n          field = get(_fields, name, {});\n\n          if (field._f) {\n            field._f.mount = false;\n          }\n\n          (_options.shouldUnregister || options.shouldUnregister) &&\n            !(isNameInFieldArray(_names.array, name) && _state.action) &&\n            _names.unMount.add(name);\n        }\n      },\n    };\n  };\n\n  const _focusError = () =>\n    _options.shouldFocusError &&\n    iterateFieldsByAction(_fields, _focusInput, _names.mount);\n\n  const _disableForm = (disabled?: boolean) => {\n    if (isBoolean(disabled)) {\n      _subjects.state.next({ disabled });\n      iterateFieldsByAction(\n        _fields,\n        (ref, name) => {\n          const currentField: Field = get(_fields, name);\n          if (currentField) {\n            ref.disabled = currentField._f.disabled || disabled;\n\n            if (Array.isArray(currentField._f.refs)) {\n              currentField._f.refs.forEach((inputRef) => {\n                inputRef.disabled = currentField._f.disabled || disabled;\n              });\n            }\n          }\n        },\n        0,\n        false,\n      );\n    }\n  };\n\n  const handleSubmit: UseFormHandleSubmit<TFieldValues, TTransformedValues> =\n    (onValid, onInvalid) => async (e) => {\n      let onValidError = undefined;\n      if (e) {\n        e.preventDefault && e.preventDefault();\n        (e as React.BaseSyntheticEvent).persist &&\n          (e as React.BaseSyntheticEvent).persist();\n      }\n      let fieldValues: TFieldValues | TTransformedValues | {} =\n        cloneObject(_formValues);\n\n      _subjects.state.next({\n        isSubmitting: true,\n      });\n\n      if (_options.resolver) {\n        const { errors, values } = await _runSchema();\n        _formState.errors = errors;\n        fieldValues = cloneObject(values) as TFieldValues;\n      } else {\n        await executeBuiltInValidation(_fields);\n      }\n\n      if (_names.disabled.size) {\n        for (const name of _names.disabled) {\n          unset(fieldValues, name);\n        }\n      }\n\n      unset(_formState.errors, 'root');\n\n      if (isEmptyObject(_formState.errors)) {\n        _subjects.state.next({\n          errors: {},\n        });\n        try {\n          await onValid(fieldValues as TTransformedValues, e);\n        } catch (error) {\n          onValidError = error;\n        }\n      } else {\n        if (onInvalid) {\n          await onInvalid({ ..._formState.errors }, e);\n        }\n        _focusError();\n        setTimeout(_focusError);\n      }\n\n      _subjects.state.next({\n        isSubmitted: true,\n        isSubmitting: false,\n        isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n        submitCount: _formState.submitCount + 1,\n        errors: _formState.errors,\n      });\n      if (onValidError) {\n        throw onValidError;\n      }\n    };\n\n  const resetField: UseFormResetField<TFieldValues> = (name, options = {}) => {\n    if (get(_fields, name)) {\n      if (isUndefined(options.defaultValue)) {\n        setValue(name, cloneObject(get(_defaultValues, name)));\n      } else {\n        setValue(\n          name,\n          options.defaultValue as Parameters<typeof setValue<typeof name>>[1],\n        );\n        set(_defaultValues, name, cloneObject(options.defaultValue));\n      }\n\n      if (!options.keepTouched) {\n        unset(_formState.touchedFields, name);\n      }\n\n      if (!options.keepDirty) {\n        unset(_formState.dirtyFields, name);\n        _formState.isDirty = options.defaultValue\n          ? _getDirty(name, cloneObject(get(_defaultValues, name)))\n          : _getDirty();\n      }\n\n      if (!options.keepError) {\n        unset(_formState.errors, name);\n        _proxyFormState.isValid && _setValid();\n      }\n\n      _subjects.state.next({ ..._formState });\n    }\n  };\n\n  const _reset: UseFormReset<TFieldValues> = (\n    formValues,\n    keepStateOptions = {},\n  ) => {\n    const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n    const cloneUpdatedValues = cloneObject(updatedValues);\n    const isEmptyResetValues = isEmptyObject(formValues);\n    const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n\n    if (!keepStateOptions.keepDefaultValues) {\n      _defaultValues = updatedValues;\n    }\n\n    if (!keepStateOptions.keepValues) {\n      if (keepStateOptions.keepDirtyValues) {\n        const fieldsToCheck = new Set([\n          ..._names.mount,\n          ...Object.keys(getDirtyFields(_defaultValues, _formValues)),\n        ]);\n        for (const fieldName of Array.from(fieldsToCheck)) {\n          get(_formState.dirtyFields, fieldName)\n            ? set(values, fieldName, get(_formValues, fieldName))\n            : setValue(\n                fieldName as FieldPath<TFieldValues>,\n                get(values, fieldName),\n              );\n        }\n      } else {\n        if (isWeb && isUndefined(formValues)) {\n          for (const name of _names.mount) {\n            const field = get(_fields, name);\n            if (field && field._f) {\n              const fieldReference = Array.isArray(field._f.refs)\n                ? field._f.refs[0]\n                : field._f.ref;\n\n              if (isHTMLElement(fieldReference)) {\n                const form = fieldReference.closest('form');\n                if (form) {\n                  form.reset();\n                  break;\n                }\n              }\n            }\n          }\n        }\n\n        if (keepStateOptions.keepFieldsRef) {\n          for (const fieldName of _names.mount) {\n            setValue(\n              fieldName as FieldPath<TFieldValues>,\n              get(values, fieldName),\n            );\n          }\n        } else {\n          _fields = {};\n        }\n      }\n\n      _formValues = _options.shouldUnregister\n        ? keepStateOptions.keepDefaultValues\n          ? (cloneObject(_defaultValues) as TFieldValues)\n          : ({} as TFieldValues)\n        : (cloneObject(values) as TFieldValues);\n\n      _subjects.array.next({\n        values: { ...values },\n      });\n\n      _subjects.state.next({\n        values: { ...values } as TFieldValues,\n      });\n    }\n\n    _names = {\n      mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n      unMount: new Set(),\n      array: new Set(),\n      disabled: new Set(),\n      watch: new Set(),\n      watchAll: false,\n      focus: '',\n    };\n\n    _state.mount =\n      !_proxyFormState.isValid ||\n      !!keepStateOptions.keepIsValid ||\n      !!keepStateOptions.keepDirtyValues;\n\n    _state.watch = !!_options.shouldUnregister;\n\n    _subjects.state.next({\n      submitCount: keepStateOptions.keepSubmitCount\n        ? _formState.submitCount\n        : 0,\n      isDirty: isEmptyResetValues\n        ? false\n        : keepStateOptions.keepDirty\n          ? _formState.isDirty\n          : !!(\n              keepStateOptions.keepDefaultValues &&\n              !deepEqual(formValues, _defaultValues)\n            ),\n      isSubmitted: keepStateOptions.keepIsSubmitted\n        ? _formState.isSubmitted\n        : false,\n      dirtyFields: isEmptyResetValues\n        ? {}\n        : keepStateOptions.keepDirtyValues\n          ? keepStateOptions.keepDefaultValues && _formValues\n            ? getDirtyFields(_defaultValues, _formValues)\n            : _formState.dirtyFields\n          : keepStateOptions.keepDefaultValues && formValues\n            ? getDirtyFields(_defaultValues, formValues)\n            : keepStateOptions.keepDirty\n              ? _formState.dirtyFields\n              : {},\n      touchedFields: keepStateOptions.keepTouched\n        ? _formState.touchedFields\n        : {},\n      errors: keepStateOptions.keepErrors ? _formState.errors : {},\n      isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful\n        ? _formState.isSubmitSuccessful\n        : false,\n      isSubmitting: false,\n    });\n  };\n\n  const reset: UseFormReset<TFieldValues> = (formValues, keepStateOptions) =>\n    _reset(\n      isFunction(formValues)\n        ? (formValues as Function)(_formValues as TFieldValues)\n        : formValues,\n      keepStateOptions,\n    );\n\n  const setFocus: UseFormSetFocus<TFieldValues> = (name, options = {}) => {\n    const field = get(_fields, name);\n    const fieldReference = field && field._f;\n\n    if (fieldReference) {\n      const fieldRef = fieldReference.refs\n        ? fieldReference.refs[0]\n        : fieldReference.ref;\n\n      if (fieldRef.focus) {\n        fieldRef.focus();\n        options.shouldSelect &&\n          isFunction(fieldRef.select) &&\n          fieldRef.select();\n      }\n    }\n  };\n\n  const _setFormState = (\n    updatedFormState: Partial<FormState<TFieldValues>>,\n  ) => {\n    _formState = {\n      ..._formState,\n      ...updatedFormState,\n    };\n  };\n\n  const _resetDefaultValues = () =>\n    isFunction(_options.defaultValues) &&\n    (_options.defaultValues as Function)().then((values: TFieldValues) => {\n      reset(values, _options.resetOptions);\n      _subjects.state.next({\n        isLoading: false,\n      });\n    });\n\n  const methods = {\n    control: {\n      register,\n      unregister,\n      getFieldState,\n      handleSubmit,\n      setError,\n      _subscribe,\n      _runSchema,\n      _focusError,\n      _getWatch,\n      _getDirty,\n      _setValid,\n      _setFieldArray,\n      _setDisabledField,\n      _setErrors,\n      _getFieldArray,\n      _reset,\n      _resetDefaultValues,\n      _removeUnmounted,\n      _disableForm,\n      _subjects,\n      _proxyFormState,\n      get _fields() {\n        return _fields;\n      },\n      get _formValues() {\n        return _formValues;\n      },\n      get _state() {\n        return _state;\n      },\n      set _state(value) {\n        _state = value;\n      },\n      get _defaultValues() {\n        return _defaultValues;\n      },\n      get _names() {\n        return _names;\n      },\n      set _names(value) {\n        _names = value;\n      },\n      get _formState() {\n        return _formState;\n      },\n      get _options() {\n        return _options;\n      },\n      set _options(value) {\n        _options = {\n          ..._options,\n          ...value,\n        };\n      },\n    },\n    subscribe,\n    trigger,\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    getValues,\n    reset,\n    resetField,\n    clearErrors,\n    unregister,\n    setError,\n    setFocus,\n    getFieldState,\n  };\n\n  return {\n    ...methods,\n    formControl: methods,\n  };\n}\n", "import type { Field } from '../types';\n\nexport default (options: Field['_f']) =>\n  options.mount &&\n  (options.required ||\n    options.min ||\n    options.max ||\n    options.maxLength ||\n    options.minLength ||\n    options.pattern ||\n    options.validate);\n", "import type { ValidationModeFlags } from '../types';\n\nexport default (\n  isBlurEvent: boolean,\n  isTouched: boolean,\n  isSubmitted: boolean,\n  reValidateMode: {\n    isOnBlur: boolean;\n    isOnChange: boolean;\n  },\n  mode: Partial<ValidationModeFlags>,\n) => {\n  if (mode.isOnAll) {\n    return false;\n  } else if (!isSubmitted && mode.isOnTouch) {\n    return !(isTouched || isBlurEvent);\n  } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n    return !isBlurEvent;\n  } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n    return isBlurEvent;\n  }\n  return true;\n};\n", "import convertToArrayPayload from '../utils/convertToArrayPayload';\n\nexport default <T extends string | readonly string[] | undefined>(\n  name?: T,\n  signalName?: string,\n  exact?: boolean,\n) =>\n  !name ||\n  !signalName ||\n  name === signalName ||\n  convertToArrayPayload(name).some(\n    (currentName) =>\n      currentName &&\n      (exact\n        ? currentName === signalName\n        : currentName.startsWith(signalName) ||\n          signalName.startsWith(currentName)),\n  );\n", "import type { FieldElement } from '../types';\n\nimport isCheckBoxInput from './isCheckBoxInput';\nimport isRadioInput from './isRadioInput';\n\nexport default (ref: FieldElement): ref is HTMLInputElement =>\n  isRadioInput(ref) || isCheckBoxInput(ref);\n", "import compact from '../utils/compact';\nimport get from '../utils/get';\nimport unset from '../utils/unset';\n\nexport default <T>(ref: T, name: string) =>\n  !compact(get(ref, name)).length && unset(ref, name);\n", "export default () => {\n  if (typeof crypto !== 'undefined' && crypto.randomUUID) {\n    return crypto.randomUUID();\n  }\n\n  const d =\n    typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n    const r = (Math.random() * 16 + d) % 16 | 0;\n\n    return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);\n  });\n};\n", "import type { FieldArrayMethodProps, InternalFieldName } from '../types';\nimport isUndefined from '../utils/isUndefined';\n\nexport default (\n  name: InternalFieldName,\n  index: number,\n  options: FieldArrayMethodProps = {},\n): string =>\n  options.shouldFocus || isUndefined(options.shouldFocus)\n    ? options.focusName ||\n      `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.`\n    : '';\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...data,\n  ...convertToArrayPayload(value),\n];\n", "export default <T>(value: T | T[]): undefined[] | undefined =>\n  Array.isArray(value) ? value.map(() => undefined) : undefined;\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default function insert<T>(data: T[], index: number): (T | undefined)[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value: T | T[],\n): T[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value?: T | T[],\n): (T | undefined)[] {\n  return [\n    ...data.slice(0, index),\n    ...convertToArrayPayload(value),\n    ...data.slice(index),\n  ];\n}\n", "import isUndefined from './isUndefined';\n\nexport default <T>(\n  data: (T | undefined)[],\n  from: number,\n  to: number,\n): (T | undefined)[] => {\n  if (!Array.isArray(data)) {\n    return [];\n  }\n\n  if (isUndefined(data[to])) {\n    data[to] = undefined;\n  }\n  data.splice(to, 0, data.splice(from, 1)[0]);\n\n  return data;\n};\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...convertToArrayPayload(value),\n  ...convertToArrayPayload(data),\n];\n", "import compact from './compact';\nimport convertToArrayPayload from './convertToArrayPayload';\nimport isUndefined from './isUndefined';\n\nfunction removeAtIndexes<T>(data: T[], indexes: number[]): T[] {\n  let i = 0;\n  const temp = [...data];\n\n  for (const index of indexes) {\n    temp.splice(index - i, 1);\n    i++;\n  }\n\n  return compact(temp).length ? temp : [];\n}\n\nexport default <T>(data: T[], index?: number | number[]): T[] =>\n  isUndefined(index)\n    ? []\n    : removeAtIndexes(\n        data,\n        (convertToArrayPayload(index) as number[]).sort((a, b) => a - b),\n      );\n", "export default <T>(data: T[], indexA: number, indexB: number): void => {\n  [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\n", "export default <T>(fieldValues: T[], index: number, value: T) => {\n  fieldValues[index] = value;\n  return fieldValues;\n};\n", "import React from 'react';\n\nimport generateId from './logic/generateId';\nimport getFocusFieldName from './logic/getFocusFieldName';\nimport getValidationModes from './logic/getValidationModes';\nimport isWatched from './logic/isWatched';\nimport iterateFieldsByAction from './logic/iterateFieldsByAction';\nimport updateFieldArrayRootError from './logic/updateFieldArrayRootError';\nimport validateField from './logic/validateField';\nimport appendAt from './utils/append';\nimport cloneObject from './utils/cloneObject';\nimport convertToArrayPayload from './utils/convertToArrayPayload';\nimport fillEmptyArray from './utils/fillEmptyArray';\nimport get from './utils/get';\nimport insertAt from './utils/insert';\nimport isEmptyObject from './utils/isEmptyObject';\nimport moveArrayAt from './utils/move';\nimport prependAt from './utils/prepend';\nimport removeArrayAt from './utils/remove';\nimport set from './utils/set';\nimport swapArrayAt from './utils/swap';\nimport unset from './utils/unset';\nimport updateAt from './utils/update';\nimport { VALIDATION_MODE } from './constants';\nimport type {\n  Control,\n  Field,\n  FieldArray,\n  FieldArrayMethodProps,\n  FieldArrayPath,\n  FieldArrayWithId,\n  FieldErrors,\n  FieldPath,\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  RegisterOptions,\n  UseFieldArrayProps,\n  UseFieldArrayReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useFieldArray<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldArrayName extends\n    FieldArrayPath<TFieldValues> = FieldArrayPath<TFieldValues>,\n  TKeyName extends string = 'id',\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFieldArrayProps<\n    TFieldValues,\n    TFieldArrayName,\n    TKeyName,\n    TTransformedValues\n  >,\n): UseFieldArrayReturn<TFieldValues, TFieldArrayName, TKeyName> {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    keyName = 'id',\n    shouldUnregister,\n    rules,\n  } = props;\n  const [fields, setFields] = React.useState(control._getFieldArray(name));\n  const ids = React.useRef<string[]>(\n    control._getFieldArray(name).map(generateId),\n  );\n  const _fieldIds = React.useRef(fields);\n  const _actioned = React.useRef(false);\n\n  _fieldIds.current = fields;\n  control._names.array.add(name);\n\n  React.useMemo(\n    () =>\n      rules &&\n      (control as Control<TFieldValues, any, TTransformedValues>).register(\n        name as FieldPath<TFieldValues>,\n        rules as RegisterOptions<TFieldValues>,\n      ),\n    [control, rules, name],\n  );\n\n  useIsomorphicLayoutEffect(\n    () =>\n      control._subjects.array.subscribe({\n        next: ({\n          values,\n          name: fieldArrayName,\n        }: {\n          values?: FieldValues;\n          name?: InternalFieldName;\n        }) => {\n          if (fieldArrayName === name || !fieldArrayName) {\n            const fieldValues = get(values, name);\n            if (Array.isArray(fieldValues)) {\n              setFields(fieldValues);\n              ids.current = fieldValues.map(generateId);\n            }\n          }\n        },\n      }).unsubscribe,\n    [control, name],\n  );\n\n  const updateValues = React.useCallback(\n    <\n      T extends Partial<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >[],\n    >(\n      updatedFieldArrayValues: T,\n    ) => {\n      _actioned.current = true;\n      control._setFieldArray(name, updatedFieldArrayValues);\n    },\n    [control, name],\n  );\n\n  const append = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const appendValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = appendAt(\n      control._getFieldArray(name),\n      appendValue,\n    );\n    control._names.focus = getFocusFieldName(\n      name,\n      updatedFieldArrayValues.length - 1,\n      options,\n    );\n    ids.current = appendAt(ids.current, appendValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const prepend = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const prependValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = prependAt(\n      control._getFieldArray(name),\n      prependValue,\n    );\n    control._names.focus = getFocusFieldName(name, 0, options);\n    ids.current = prependAt(ids.current, prependValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const remove = (index?: number | number[]) => {\n    const updatedFieldArrayValues: Partial<\n      FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n    >[] = removeArrayAt(control._getFieldArray(name), index);\n    ids.current = removeArrayAt(ids.current, index);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    !Array.isArray(get(control._fields, name)) &&\n      set(control._fields, name, undefined);\n    control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n      argA: index,\n    });\n  };\n\n  const insert = (\n    index: number,\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const insertValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = insertAt(\n      control._getFieldArray(name),\n      index,\n      insertValue,\n    );\n    control._names.focus = getFocusFieldName(name, index, options);\n    ids.current = insertAt(ids.current, index, insertValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, insertAt, {\n      argA: index,\n      argB: fillEmptyArray(value),\n    });\n  };\n\n  const swap = (indexA: number, indexB: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n    swapArrayAt(ids.current, indexA, indexB);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      swapArrayAt,\n      {\n        argA: indexA,\n        argB: indexB,\n      },\n      false,\n    );\n  };\n\n  const move = (from: number, to: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    moveArrayAt(updatedFieldArrayValues, from, to);\n    moveArrayAt(ids.current, from, to);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      moveArrayAt,\n      {\n        argA: from,\n        argB: to,\n      },\n      false,\n    );\n  };\n\n  const update = (\n    index: number,\n    value: FieldArray<TFieldValues, TFieldArrayName>,\n  ) => {\n    const updateValue = cloneObject(value);\n    const updatedFieldArrayValues = updateAt(\n      control._getFieldArray<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >(name),\n      index,\n      updateValue as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>,\n    );\n    ids.current = [...updatedFieldArrayValues].map((item, i) =>\n      !item || i === index ? generateId() : ids.current[i],\n    );\n    updateValues(updatedFieldArrayValues);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      updateAt,\n      {\n        argA: index,\n        argB: updateValue,\n      },\n      true,\n      false,\n    );\n  };\n\n  const replace = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n  ) => {\n    const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n    ids.current = updatedFieldArrayValues.map(generateId);\n    updateValues([...updatedFieldArrayValues]);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(\n      name,\n      [...updatedFieldArrayValues],\n      <T>(data: T): T => data,\n      {},\n      true,\n      false,\n    );\n  };\n\n  React.useEffect(() => {\n    control._state.action = false;\n\n    isWatched(name, control._names) &&\n      control._subjects.state.next({\n        ...control._formState,\n      } as FormState<TFieldValues>);\n\n    if (\n      _actioned.current &&\n      (!getValidationModes(control._options.mode).isOnSubmit ||\n        control._formState.isSubmitted) &&\n      !getValidationModes(control._options.reValidateMode).isOnSubmit\n    ) {\n      if (control._options.resolver) {\n        control._runSchema([name]).then((result) => {\n          const error = get(result.errors, name);\n          const existingError = get(control._formState.errors, name);\n\n          if (\n            existingError\n              ? (!error && existingError.type) ||\n                (error &&\n                  (existingError.type !== error.type ||\n                    existingError.message !== error.message))\n              : error && error.type\n          ) {\n            error\n              ? set(control._formState.errors, name, error)\n              : unset(control._formState.errors, name);\n            control._subjects.state.next({\n              errors: control._formState.errors as FieldErrors<TFieldValues>,\n            });\n          }\n        });\n      } else {\n        const field: Field = get(control._fields, name);\n        if (\n          field &&\n          field._f &&\n          !(\n            getValidationModes(control._options.reValidateMode).isOnSubmit &&\n            getValidationModes(control._options.mode).isOnSubmit\n          )\n        ) {\n          validateField(\n            field,\n            control._names.disabled,\n            control._formValues,\n            control._options.criteriaMode === VALIDATION_MODE.all,\n            control._options.shouldUseNativeValidation,\n            true,\n          ).then(\n            (error) =>\n              !isEmptyObject(error) &&\n              control._subjects.state.next({\n                errors: updateFieldArrayRootError(\n                  control._formState.errors as FieldErrors<TFieldValues>,\n                  error,\n                  name,\n                ) as FieldErrors<TFieldValues>,\n              }),\n          );\n        }\n      }\n    }\n\n    control._subjects.state.next({\n      name,\n      values: cloneObject(control._formValues) as TFieldValues,\n    });\n\n    control._names.focus &&\n      iterateFieldsByAction(control._fields, (ref, key: string) => {\n        if (\n          control._names.focus &&\n          key.startsWith(control._names.focus) &&\n          ref.focus\n        ) {\n          ref.focus();\n          return 1;\n        }\n        return;\n      });\n\n    control._names.focus = '';\n\n    control._setValid();\n    _actioned.current = false;\n  }, [fields, name, control]);\n\n  React.useEffect(() => {\n    !get(control._formValues, name) && control._setFieldArray(name);\n\n    return () => {\n      const updateMounted = (name: InternalFieldName, value: boolean) => {\n        const field: Field = get(control._fields, name);\n        if (field && field._f) {\n          field._f.mount = value;\n        }\n      };\n\n      control._options.shouldUnregister || shouldUnregister\n        ? control.unregister(name as FieldPath<TFieldValues>)\n        : updateMounted(name, false);\n    };\n  }, [name, control, keyName, shouldUnregister]);\n\n  return {\n    swap: React.useCallback(swap, [updateValues, name, control]),\n    move: React.useCallback(move, [updateValues, name, control]),\n    prepend: React.useCallback(prepend, [updateValues, name, control]),\n    append: React.useCallback(append, [updateValues, name, control]),\n    remove: React.useCallback(remove, [updateValues, name, control]),\n    insert: React.useCallback(insert, [updateValues, name, control]),\n    update: React.useCallback(update, [updateValues, name, control]),\n    replace: React.useCallback(replace, [updateValues, name, control]),\n    fields: React.useMemo(\n      () =>\n        fields.map((field, index) => ({\n          ...field,\n          [keyName]: ids.current[index] || generateId(),\n        })) as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>[],\n      [fields, keyName],\n    ),\n  };\n}\n", "import React from 'react';\n\nimport getProxyFormState from './logic/getProxyFormState';\nimport deepEqual from './utils/deepEqual';\nimport isFunction from './utils/isFunction';\nimport { createFormControl } from './logic';\nimport type {\n  FieldValues,\n  FormState,\n  UseFormProps,\n  UseFormReturn,\n} from './types';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useForm<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFormProps<TFieldValues, TContext, TTransformedValues> = {},\n): UseFormReturn<TFieldValues, TContext, TTransformedValues> {\n  const _formControl = React.useRef<\n    UseFormReturn<TFieldValues, TContext, TTransformedValues> | undefined\n  >(undefined);\n  const _values = React.useRef<typeof props.values>(undefined);\n  const [formState, updateFormState] = React.useState<FormState<TFieldValues>>({\n    isDirty: false,\n    isValidating: false,\n    isLoading: isFunction(props.defaultValues),\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    submitCount: 0,\n    dirtyFields: {},\n    touchedFields: {},\n    validatingFields: {},\n    errors: props.errors || {},\n    disabled: props.disabled || false,\n    isReady: false,\n    defaultValues: isFunction(props.defaultValues)\n      ? undefined\n      : props.defaultValues,\n  });\n\n  if (!_formControl.current) {\n    if (props.formControl) {\n      _formControl.current = {\n        ...props.formControl,\n        formState,\n      };\n\n      if (props.defaultValues && !isFunction(props.defaultValues)) {\n        props.formControl.reset(props.defaultValues, props.resetOptions);\n      }\n    } else {\n      const { formControl, ...rest } = createFormControl(props);\n\n      _formControl.current = {\n        ...rest,\n        formState,\n      };\n    }\n  }\n\n  const control = _formControl.current.control;\n  control._options = props;\n\n  useIsomorphicLayoutEffect(() => {\n    const sub = control._subscribe({\n      formState: control._proxyFormState,\n      callback: () => updateFormState({ ...control._formState }),\n      reRenderRoot: true,\n    });\n\n    updateFormState((data) => ({\n      ...data,\n      isReady: true,\n    }));\n\n    control._formState.isReady = true;\n\n    return sub;\n  }, [control]);\n\n  React.useEffect(\n    () => control._disableForm(props.disabled),\n    [control, props.disabled],\n  );\n\n  React.useEffect(() => {\n    if (props.mode) {\n      control._options.mode = props.mode;\n    }\n    if (props.reValidateMode) {\n      control._options.reValidateMode = props.reValidateMode;\n    }\n  }, [control, props.mode, props.reValidateMode]);\n\n  React.useEffect(() => {\n    if (props.errors) {\n      control._setErrors(props.errors);\n      control._focusError();\n    }\n  }, [control, props.errors]);\n\n  React.useEffect(() => {\n    props.shouldUnregister &&\n      control._subjects.state.next({\n        values: control._getWatch(),\n      });\n  }, [control, props.shouldUnregister]);\n\n  React.useEffect(() => {\n    if (control._proxyFormState.isDirty) {\n      const isDirty = control._getDirty();\n      if (isDirty !== formState.isDirty) {\n        control._subjects.state.next({\n          isDirty,\n        });\n      }\n    }\n  }, [control, formState.isDirty]);\n\n  React.useEffect(() => {\n    if (props.values && !deepEqual(props.values, _values.current)) {\n      control._reset(props.values, {\n        keepFieldsRef: true,\n        ...control._options.resetOptions,\n      });\n      _values.current = props.values;\n      updateFormState((state) => ({ ...state }));\n    } else {\n      control._resetDefaultValues();\n    }\n  }, [control, props.values]);\n\n  React.useEffect(() => {\n    if (!control._state.mount) {\n      control._setValid();\n      control._state.mount = true;\n    }\n\n    if (control._state.watch) {\n      control._state.watch = false;\n      control._subjects.state.next({ ...control._formState });\n    }\n\n    control._removeUnmounted();\n  });\n\n  _formControl.current.formState = getProxyFormState(formState, control);\n\n  return _formControl.current;\n}\n"], "names": ["isCheckBoxInput", "element", "type", "isDateObject", "value", "Date", "isNullOrUndefined", "isObjectType", "isObject", "Array", "isArray", "getEventValue", "event", "target", "checked", "isNameInFieldArray", "names", "name", "has", "substring", "search", "getNodeParentName", "isWeb", "window", "HTMLElement", "document", "cloneObject", "data", "copy", "isFileListInstance", "FileList", "Blob", "tempObject", "prototypeCopy", "constructor", "prototype", "hasOwnProperty", "isPlainObject", "key", "is<PERSON>ey", "test", "isUndefined", "val", "undefined", "compact", "filter", "Boolean", "stringToPath", "input", "replace", "split", "get", "object", "path", "defaultValue", "result", "reduce", "isBoolean", "set", "index", "temp<PERSON>ath", "length", "lastIndex", "newValue", "objValue", "isNaN", "EVENTS", "VALIDATION_MODE", "INPUT_VALIDATION_RULES", "HookFormContext", "React", "createContext", "displayName", "useFormContext", "useContext", "getProxyFormState", "formState", "control", "localProxyFormState", "isRoot", "defaultValues", "_defaultValues", "Object", "defineProperty", "_key", "_proxyFormState", "useIsomorphicLayoutEffect", "useLayoutEffect", "useEffect", "useFormState", "props", "methods", "disabled", "exact", "updateFormState", "useState", "_formState", "_localProxyFormState", "useRef", "isDirty", "isLoading", "dirtyFields", "touchedFields", "validatingFields", "isValidating", "<PERSON><PERSON><PERSON><PERSON>", "errors", "_subscribe", "current", "callback", "_setValid", "useMemo", "isString", "generateWatchOutput", "_names", "formValues", "isGlobal", "watch", "add", "map", "fieldName", "watchAll", "isPrimitive", "deepEqual", "object1", "object2", "_internal_visited", "WeakSet", "getTime", "keys1", "keys", "keys2", "val1", "includes", "val2", "useWatch", "compute", "_defaultValue", "_compute", "_computeFormValues", "defaultValueMemo", "_getWatch", "updateValue", "values", "_formValues", "computedFormValues", "_removeUnmounted", "useController", "shouldUnregister", "isArrayField", "array", "_props", "_registerProps", "register", "rules", "fieldState", "defineProperties", "invalid", "enumerable", "isTouched", "error", "onChange", "useCallback", "onBlur", "ref", "elm", "field", "_fields", "_f", "focus", "select", "setCustomValidity", "message", "reportValidity", "_shouldUnregisterField", "_options", "updateMounted", "mount", "_state", "action", "unregister", "_setDisabledField", "flatten", "obj", "output", "nested", "nested<PERSON><PERSON>", "POST_REQUEST", "appendErrors", "validateAllFieldCriteria", "types", "convertToArrayPayload", "createSubject", "_observers", "observers", "next", "observer", "subscribe", "push", "unsubscribe", "o", "isEmptyObject", "isFileInput", "isFunction", "isHTMLElement", "owner", "ownerDocument", "defaultView", "isMultipleSelect", "isRadioInput", "live", "isConnected", "unset", "paths", "childObject", "updatePath", "slice", "baseGet", "isEmptyArray", "objectHasFunction", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fields", "isParentNodeArray", "getDirtyFieldsFromDefaultValues", "dirtyField<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDirty<PERSON>ields", "defaultResult", "validResult", "getCheckboxValue", "options", "option", "attributes", "getFieldValueAs", "valueAsNumber", "valueAsDate", "setValueAs", "NaN", "defaultReturn", "getRadioValue", "previous", "getFieldValue", "files", "refs", "selectedOptions", "isCheckBox", "isRegex", "RegExp", "getRuleValue", "rule", "source", "getValidationModes", "mode", "isOnSubmit", "isOnBlur", "isOnChange", "isOnAll", "isOnTouch", "ASYNC_FUNCTION", "hasPromiseValidation", "fieldReference", "validate", "find", "validateFunction", "isWatched", "isBlurEvent", "some", "watchName", "startsWith", "iterateFieldsByAction", "fieldsNames", "abort<PERSON><PERSON><PERSON>", "current<PERSON><PERSON>", "schemaErrorLookup", "join", "found<PERSON><PERSON>r", "root", "pop", "updateFieldArrayRootError", "fieldArrayErrors", "isMessage", "getValidateError", "every", "getValueAndMessage", "validationData", "validateField", "async", "disabled<PERSON>ieldN<PERSON>s", "shouldUseNativeValidation", "isFieldArray", "required", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "min", "max", "pattern", "inputValue", "inputRef", "isRadio", "isRadioOrCheckbox", "isEmpty", "appendErrors<PERSON><PERSON><PERSON>", "bind", "getMinMaxMessage", "exceedMax", "maxLengthMessage", "minLengthMessage", "maxType", "minType", "exceedMin", "maxOutput", "minOutput", "valueDate", "convertTimeToDate", "time", "toDateString", "isTime", "isWeek", "valueNumber", "maxLengthOutput", "minLengthOutput", "patternValue", "match", "validateError", "validationResult", "defaultOptions", "reValidateMode", "shouldFocusError", "createFormControl", "delayError<PERSON><PERSON><PERSON>", "submitCount", "isReady", "isSubmitted", "isSubmitting", "isSubmitSuccessful", "Set", "unMount", "timer", "_proxySubscribeFormState", "_subjects", "state", "shouldDisplayAllAssociatedErrors", "criteriaMode", "shouldUpdateValid", "resolver", "_runSchema", "executeBuiltInValidation", "_updateIsValidating", "from", "for<PERSON>ach", "updateValidAndValue", "shouldSkipSetValueAs", "defaultChecked", "setFieldValue", "updateTouchAndDirty", "fieldValue", "should<PERSON>irty", "shouldRender", "shouldUpdateField", "is<PERSON>revious<PERSON><PERSON>y", "_getDirty", "isCurrentFieldPristine", "isPreviousFieldTouched", "shouldRenderByError", "previousFieldError", "delayError", "updateErrors", "wait", "clearTimeout", "setTimeout", "updatedFormState", "context", "getResolverOptions", "should<PERSON>nly<PERSON><PERSON><PERSON><PERSON>d", "valid", "isFieldArrayRoot", "isPromiseFunction", "fieldError", "getV<PERSON>ues", "optionRef", "selected", "checkboxRef", "radioRef", "shouldTouch", "shouldValidate", "trigger", "set<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "setValue", "cloneValue", "isFieldValueUpdated", "_updateIsFieldValueUpdated", "Number", "validationModeBeforeSubmit", "validationModeAfterSubmit", "shouldSkipValidation", "deps", "skipValidation", "watched", "previousErrorLookupResult", "errorLookupResult", "_focusInput", "fieldNames", "executeSchemaAndUpdateState", "Promise", "all", "shouldFocus", "getFieldState", "setError", "currentError", "currentRef", "restOfErrorTree", "signalName", "currentName", "formStateData", "shouldRenderFormState", "_setFormState", "reRenderRoot", "delete", "keepValue", "keepError", "keep<PERSON>irty", "keepTouched", "keepIsValidating", "keepDefaultValue", "keepIsValid", "disabledIsDefined", "progressive", "fieldRef", "querySelectorAll", "radioOrCheckbox", "_focusError", "handleSubmit", "onValid", "onInvalid", "e", "onValidError", "preventDefault", "persist", "field<PERSON><PERSON><PERSON>", "size", "_reset", "keepStateOptions", "updatedValues", "cloneUpdatedValues", "isEmptyResetValues", "keepDefaultValues", "keepV<PERSON>ues", "keepDirtyV<PERSON>ues", "fieldsToCheck", "form", "closest", "reset", "keepFieldsRef", "keepSubmitCount", "keepIsSubmitted", "keepErrors", "keepIsSubmitSuccessful", "_setFieldArray", "method", "args", "shouldSetValues", "shouldUpdateFieldsAndState", "argA", "argB", "unsetEmptyArray", "_setErrors", "_getFieldArray", "_resetDefaultValues", "then", "resetOptions", "_disableForm", "payload", "reset<PERSON>ield", "clearErrors", "inputName", "setFocus", "shouldSelect", "formControl", "generateId", "crypto", "randomUUID", "d", "performance", "now", "c", "r", "Math", "random", "toString", "getFocusFieldName", "focusName", "focusIndex", "appendAt", "fillEmptyArray", "insert", "moveArrayAt", "to", "splice", "prependAt", "removeArrayAt", "indexes", "i", "temp", "removeAtIndexes", "sort", "a", "b", "swapArrayAt", "indexA", "indexB", "updateAt", "render", "mounted", "setMounted", "onSubmit", "children", "headers", "encType", "onError", "onSuccess", "validateStatus", "rest", "submit", "<PERSON><PERSON><PERSON><PERSON>", "formData", "FormData", "formDataJson", "JSON", "stringify", "_a", "flattenForm<PERSON><PERSON>ues", "append", "shouldStringifySubmissionData", "response", "fetch", "String", "body", "status", "createElement", "Fragment", "noValidate", "Provider", "keyName", "setFields", "ids", "_fieldIds", "_actioned", "fieldArrayName", "updateValues", "updatedFieldArrayValues", "existingError", "swap", "move", "prepend", "prependValue", "appendValue", "remove", "insertValue", "insertAt", "update", "item", "_formControl", "_values", "sub"], "mappings": "uRAEA,IAAAA,EAAgBC,GACG,aAAjBA,EAAQC,KCHVC,EAAgBC,GAAkCA,aAAiBC,KCAnEC,EAAgBF,GAAuD,MAATA,ECGvD,MAAMG,EAAgBH,GACV,iBAAVA,EAET,IAAAI,EAAkCJ,IAC/BE,EAAkBF,KAClBK,MAAMC,QAAQN,IACfG,EAAaH,KACZD,EAAaC,GCLhBO,EAAgBC,GACdJ,EAASI,IAAWA,EAAgBC,OAChCb,EAAiBY,EAAgBC,QAC9BD,EAAgBC,OAAOC,QACvBF,EAAgBC,OAAOT,MAC1BQ,ECNNG,EAAe,CAACC,EAA+BC,IAC7CD,EAAME,ICLO,CAACD,GACdA,EAAKE,UAAU,EAAGF,EAAKG,OAAO,iBAAmBH,EDIvCI,CAAkBJ,IEL9BK,EAAiC,oBAAXC,aACU,IAAvBA,OAAOC,aACM,oBAAbC,SCEK,SAAUC,EAAeC,GACrC,IAAIC,EACJ,MAAMlB,EAAUD,MAAMC,QAAQiB,GACxBE,EACgB,oBAAbC,UAA2BH,aAAgBG,SAEpD,GAAIH,aAAgBtB,KAClBuB,EAAO,IAAIvB,KAAKsB,OACX,IACHL,IAAUK,aAAgBI,MAAQF,KACnCnB,IAAWF,EAASmB,GAcrB,OAAOA,EAVP,GAFAC,EAAOlB,EAAU,GAAK,CAAA,EAEjBA,GChBM,CAACsB,IACd,MAAMC,EACJD,EAAWE,aAAeF,EAAWE,YAAYC,UAEnD,OACE3B,EAASyB,IAAkBA,EAAcG,eAAe,kBDWvCC,CAAcV,GAG7B,IAAK,MAAMW,KAAOX,EACZA,EAAKS,eAAeE,KACtBV,EAAKU,GAAOZ,EAAYC,EAAKW,UAJjCV,EAAOD,EAYX,OAAOC,CACT,CEhCA,IAAAW,EAAgBnC,GAAkB,QAAQoC,KAAKpC,GCA/CqC,EAAgBC,QAA2CC,IAARD,ECAnDE,EAAwBxC,GACtBK,MAAMC,QAAQN,GAASA,EAAMyC,OAAOC,SAAW,GCCjDC,EAAgBC,GACdJ,EAAQI,EAAMC,QAAQ,YAAa,IAAIC,MAAM,UCG/CC,EAAe,CACbC,EACAC,EACAC,KAEA,IAAKD,IAAS7C,EAAS4C,GACrB,OAAOE,EAGT,MAAMC,GAAUhB,EAAMc,GAAQ,CAACA,GAAQN,EAAaM,IAAOG,OACzD,CAACD,EAAQjB,IACPhC,EAAkBiD,GAAUA,EAASA,EAAOjB,GAC9Cc,GAGF,OAAOX,EAAYc,IAAWA,IAAWH,EACrCX,EAAYW,EAAOC,IACjBC,EACAF,EAAOC,GACTE,GCzBNE,EAAgBrD,GAAsD,kBAAVA,ECM5DsD,EAAe,CACbN,EACAC,EACAjD,KAEA,IAAIuD,GAAQ,EACZ,MAAMC,EAAWrB,EAAMc,GAAQ,CAACA,GAAQN,EAAaM,GAC/CQ,EAASD,EAASC,OAClBC,EAAYD,EAAS,EAE3B,OAASF,EAAQE,GAAQ,CACvB,MAAMvB,EAAMsB,EAASD,GACrB,IAAII,EAAW3D,EAEf,GAAIuD,IAAUG,EAAW,CACvB,MAAME,EAAWZ,EAAOd,GACxByB,EACEvD,EAASwD,IAAavD,MAAMC,QAAQsD,GAChCA,EACCC,OAAOL,EAASD,EAAQ,IAEvB,CAAA,EADA,GAIV,GAAY,cAARrB,GAA+B,gBAARA,GAAiC,cAARA,EAClD,OAGFc,EAAOd,GAAOyB,EACdX,EAASA,EAAOd,KCnCb,MAAM4B,EACL,OADKA,EAEA,WAFAA,EAGH,SAGGC,EACH,SADGA,EAED,WAFCA,EAGD,WAHCA,EAIA,YAJAA,EAKN,MAGMC,EACN,MADMA,EAEN,MAFMA,EAGA,YAHAA,EAIA,YAJAA,EAKF,UALEA,EAMD,WANCA,EAOD,WCjBNC,EAAkBC,EAAMC,cAAoC,MAClEF,EAAgBG,YAAc,kBAgCvB,MAAMC,EAAiB,IAK5BH,EAAMI,WAAWL,GCvCnB,IAAAM,EAAe,CAKbC,EACAC,EACAC,EACAC,GAAS,KAET,MAAMxB,EAAS,CACbyB,cAAeH,EAAQI,gBAGzB,IAAK,MAAM3C,KAAOsC,EAChBM,OAAOC,eAAe5B,EAAQjB,EAAK,CACjCa,IAAK,KACH,MAAMiC,EAAO9C,EAOb,OALIuC,EAAQQ,gBAAgBD,KAAUjB,IACpCU,EAAQQ,gBAAgBD,IAASL,GAAUZ,GAG7CW,IAAwBA,EAAoBM,IAAQ,GAC7CR,EAAUQ,MAKvB,OAAO7B,GC9BF,MAAM+B,EACO,oBAAX/D,OAAyB+C,EAAMiB,gBAAkBjB,EAAMkB,UCsC1D,SAAUC,EAIdC,GAEA,MAAMC,EAAUlB,KACVI,QAAEA,EAAUc,EAAQd,QAAOe,SAAEA,EAAQ3E,KAAEA,EAAI4E,MAAEA,GAAUH,GAAS,CAAA,GAC/Dd,EAAWkB,GAAmBxB,EAAMyB,SAASlB,EAAQmB,YACtDC,EAAuB3B,EAAM4B,OAAO,CACxCC,SAAS,EACTC,WAAW,EACXC,aAAa,EACbC,eAAe,EACfC,kBAAkB,EAClBC,cAAc,EACdC,SAAS,EACTC,QAAQ,IAwBV,OArBApB,EACE,IACET,EAAQ8B,WAAW,CACjB1F,OACA2D,UAAWqB,EAAqBW,QAChCf,QACAgB,SAAWjC,KACRgB,GACCE,EAAgB,IACXjB,EAAQmB,cACRpB,OAIb,CAAC3D,EAAM2E,EAAUC,IAGnBvB,EAAMkB,UAAU,KACdS,EAAqBW,QAAQH,SAAW5B,EAAQiC,WAAU,IACzD,CAACjC,IAEGP,EAAMyC,QACX,IACEpC,EACEC,EACAC,EACAoB,EAAqBW,SACrB,GAEJ,CAAChC,EAAWC,GAEhB,CC5FA,IAAAmC,EAAgB5G,GAAqD,iBAAVA,ECI3D6G,EAAe,CACbjG,EACAkG,EACAC,EACAC,EACA9D,IAEI0D,EAAShG,IACXoG,GAAYF,EAAOG,MAAMC,IAAItG,GACtBmC,EAAIgE,EAAYnG,EAAOsC,IAG5B7C,MAAMC,QAAQM,GACTA,EAAMuG,IACVC,IACCJ,GAAYF,EAAOG,MAAMC,IAAIE,GAC7BrE,EAAIgE,EAAYK,MAKtBJ,IAAaF,EAAOO,UAAW,GAExBN,GCtBTO,EAAgBtH,GACdE,EAAkBF,KAAWG,EAAaH,GCD9B,SAAUuH,EACtBC,EACAC,EACAC,EAAoB,IAAIC,SAExB,GAAIL,EAAYE,IAAYF,EAAYG,GACtC,OAAOD,IAAYC,EAGrB,GAAI1H,EAAayH,IAAYzH,EAAa0H,GACxC,OAAOD,EAAQI,YAAcH,EAAQG,UAGvC,MAAMC,EAAQ/C,OAAOgD,KAAKN,GACpBO,EAAQjD,OAAOgD,KAAKL,GAE1B,GAAII,EAAMpE,SAAWsE,EAAMtE,OACzB,OAAO,EAGT,GAAIiE,EAAkB5G,IAAI0G,IAAYE,EAAkB5G,IAAI2G,GAC1D,OAAO,EAETC,EAAkBR,IAAIM,GACtBE,EAAkBR,IAAIO,GAEtB,IAAK,MAAMvF,KAAO2F,EAAO,CACvB,MAAMG,EAAOR,EAAQtF,GAErB,IAAK6F,EAAME,SAAS/F,GAClB,OAAO,EAGT,GAAY,QAARA,EAAe,CACjB,MAAMgG,EAAOT,EAAQvF,GAErB,GACGnC,EAAaiI,IAASjI,EAAamI,IACnC9H,EAAS4H,IAAS5H,EAAS8H,IAC3B7H,MAAMC,QAAQ0H,IAAS3H,MAAMC,QAAQ4H,IACjCX,EAAUS,EAAME,EAAMR,GACvBM,IAASE,EAEb,OAAO,GAKb,OAAO,CACT,CCyMM,SAAUC,EACd7C,GAEA,MAAMC,EAAUlB,KACVI,QACJA,EAAUc,EAAQd,QAAO5D,KACzBA,EAAIqC,aACJA,EAAYsC,SACZA,EAAQC,MACRA,EAAK2C,QACLA,GACE9C,GAAS,CAAA,EACP+C,EAAgBnE,EAAM4B,OAAO5C,GAC7BoF,EAAWpE,EAAM4B,OAAOsC,GACxBG,EAAqBrE,EAAM4B,YAAOvD,GAExC+F,EAAS9B,QAAU4B,EAEnB,MAAMI,EAAmBtE,EAAMyC,QAC7B,IACElC,EAAQgE,UACN5H,EACAwH,EAAc7B,SAElB,CAAC/B,EAAS5D,KAGLb,EAAO0I,GAAexE,EAAMyB,SACjC2C,EAAS9B,QAAU8B,EAAS9B,QAAQgC,GAAoBA,GAuC1D,OApCAtD,EACE,IACET,EAAQ8B,WAAW,CACjB1F,OACA2D,UAAW,CACTmE,QAAQ,GAEVlD,QACAgB,SAAWjC,IACT,IAAKgB,EAAU,CACb,MAAMuB,EAAaF,EACjBhG,EACA4D,EAAQqC,OACRtC,EAAUmE,QAAUlE,EAAQmE,aAC5B,EACAP,EAAc7B,SAGhB,GAAI8B,EAAS9B,QAAS,CACpB,MAAMqC,EAAqBP,EAAS9B,QAAQO,GAEvCQ,EAAUsB,EAAoBN,EAAmB/B,WACpDkC,EAAYG,GACZN,EAAmB/B,QAAUqC,QAG/BH,EAAY3B,OAKtB,CAACtC,EAASe,EAAU3E,EAAM4E,IAG5BvB,EAAMkB,UAAU,IAAMX,EAAQqE,oBAEvB9I,CACT,CCnRM,SAAU+I,EAKdzD,GAEA,MAAMC,EAAUlB,KACVxD,KACJA,EAAI2E,SACJA,EAAQf,QACRA,EAAUc,EAAQd,QAAOuE,iBACzBA,EAAgB9F,aAChBA,GACEoC,EACE2D,EAAetI,EAAmB8D,EAAQqC,OAAOoC,MAAOrI,GAExD2H,EAAmBtE,EAAMyC,QAC7B,IACE5D,EACE0B,EAAQmE,YACR/H,EACAkC,EAAI0B,EAAQI,eAAgBhE,EAAMqC,IAEtC,CAACuB,EAAS5D,EAAMqC,IAGZlD,EAAQmI,EAAS,CACrB1D,UACA5D,OACAqC,aAAcsF,EACd/C,OAAO,IAGHjB,EAAYa,EAAa,CAC7BZ,UACA5D,OACA4E,OAAO,IAGH0D,EAASjF,EAAM4B,OAAOR,GAEtB8D,EAAiBlF,EAAM4B,OAC3BrB,EAAQ4E,SAASxI,EAAM,IAClByE,EAAMgE,MACTtJ,WACIqD,EAAUiC,EAAME,UAAY,CAAEA,SAAUF,EAAME,UAAa,MAInE2D,EAAO3C,QAAUlB,EAEjB,MAAMiE,EAAarF,EAAMyC,QACvB,IACE7B,OAAO0E,iBACL,GACA,CACEC,QAAS,CACPC,YAAY,EACZ3G,IAAK,MAAQA,EAAIyB,EAAU8B,OAAQzF,IAErCkF,QAAS,CACP2D,YAAY,EACZ3G,IAAK,MAAQA,EAAIyB,EAAUyB,YAAapF,IAE1C8I,UAAW,CACTD,YAAY,EACZ3G,IAAK,MAAQA,EAAIyB,EAAU0B,cAAerF,IAE5CuF,aAAc,CACZsD,YAAY,EACZ3G,IAAK,MAAQA,EAAIyB,EAAU2B,iBAAkBtF,IAE/C+I,MAAO,CACLF,YAAY,EACZ3G,IAAK,IAAMA,EAAIyB,EAAU8B,OAAQzF,MAIzC,CAAC2D,EAAW3D,IAGRgJ,EAAW3F,EAAM4F,YACpBtJ,GACC4I,EAAe5C,QAAQqD,SAAS,CAC9BpJ,OAAQ,CACNT,MAAOO,EAAcC,GACrBK,KAAMA,GAERf,KAAMgE,IAEV,CAACjD,IAGGkJ,EAAS7F,EAAM4F,YACnB,IACEV,EAAe5C,QAAQuD,OAAO,CAC5BtJ,OAAQ,CACNT,MAAO+C,EAAI0B,EAAQmE,YAAa/H,GAChCA,KAAMA,GAERf,KAAMgE,IAEV,CAACjD,EAAM4D,EAAQmE,cAGXoB,EAAM9F,EAAM4F,YACfG,IACC,MAAMC,EAAQnH,EAAI0B,EAAQ0F,QAAStJ,GAE/BqJ,GAASD,IACXC,EAAME,GAAGJ,IAAM,CACbK,MAAO,IAAMJ,EAAII,OAASJ,EAAII,QAC9BC,OAAQ,IAAML,EAAIK,QAAUL,EAAIK,SAChCC,kBAAoBC,GAClBP,EAAIM,kBAAkBC,GACxBC,eAAgB,IAAMR,EAAIQ,oBAIhC,CAAChG,EAAQ0F,QAAStJ,IAGdqJ,EAAQhG,EAAMyC,QAClB,KAAA,CACE9F,OACAb,WACIqD,EAAUmC,IAAahB,EAAUgB,SACjC,CAAEA,SAAUhB,EAAUgB,UAAYA,GAClC,GACJqE,WACAE,SACAC,QAEF,CAACnJ,EAAM2E,EAAUhB,EAAUgB,SAAUqE,EAAUE,EAAQC,EAAKhK,IAoD9D,OAjDAkE,EAAMkB,UAAU,KACd,MAAMsF,EACJjG,EAAQkG,SAAS3B,kBAAoBA,EAEvCvE,EAAQ4E,SAASxI,EAAM,IAClBsI,EAAO3C,QAAQ8C,SACdjG,EAAU8F,EAAO3C,QAAQhB,UACzB,CAAEA,SAAU2D,EAAO3C,QAAQhB,UAC3B,KAGN,MAAMoF,EAAgB,CAAC/J,EAAyBb,KAC9C,MAAMkK,EAAenH,EAAI0B,EAAQ0F,QAAStJ,GAEtCqJ,GAASA,EAAME,KACjBF,EAAME,GAAGS,MAAQ7K,IAMrB,GAFA4K,EAAc/J,GAAM,GAEhB6J,EAAwB,CAC1B,MAAM1K,EAAQsB,EAAYyB,EAAI0B,EAAQkG,SAAS/F,cAAe/D,IAC9DyC,EAAImB,EAAQI,eAAgBhE,EAAMb,GAC9BqC,EAAYU,EAAI0B,EAAQmE,YAAa/H,KACvCyC,EAAImB,EAAQmE,YAAa/H,EAAMb,GAMnC,OAFCiJ,GAAgBxE,EAAQ4E,SAASxI,GAE3B,MAEHoI,EACIyB,IAA2BjG,EAAQqG,OAAOC,OAC1CL,GAEFjG,EAAQuG,WAAWnK,GACnB+J,EAAc/J,GAAM,KAEzB,CAACA,EAAM4D,EAASwE,EAAcD,IAEjC9E,EAAMkB,UAAU,KACdX,EAAQwG,kBAAkB,CACxBzF,WACA3E,UAED,CAAC2E,EAAU3E,EAAM4D,IAEbP,EAAMyC,QACX,KAAA,CACEuD,QACA1F,YACA+E,eAEF,CAACW,EAAO1F,EAAW+E,GAEvB,CCrMA,MCzCa2B,EAAWC,IACtB,MAAMC,EAAsB,CAAA,EAE5B,IAAK,MAAMlJ,KAAO4C,OAAOgD,KAAKqD,GAC5B,GAAIhL,EAAagL,EAAIjJ,KAAsB,OAAbiJ,EAAIjJ,GAAe,CAC/C,MAAMmJ,EAASH,EAAQC,EAAIjJ,IAE3B,IAAK,MAAMoJ,KAAaxG,OAAOgD,KAAKuD,GAClCD,EAAO,GAAGlJ,KAAOoJ,KAAeD,EAAOC,QAGzCF,EAAOlJ,GAAOiJ,EAAIjJ,GAItB,OAAOkJ,GCbHG,EAAe,OCArB,IAAAC,EAAe,CACb3K,EACA4K,EACAnF,EACAxG,EACA0K,IAEAiB,EACI,IACKnF,EAAOzF,GACV6K,MAAO,IACDpF,EAAOzF,IAASyF,EAAOzF,GAAO6K,MAAQpF,EAAOzF,GAAO6K,MAAQ,CAAA,EAChE5L,CAACA,GAAO0K,IAAW,IAGvB,CAAA,ECrBNmB,EAAmB3L,GAAcK,MAAMC,QAAQN,GAASA,EAAQ,CAACA,GCgBjE4L,EAAe,KACb,IAAIC,EAA4B,GAqBhC,MAAO,CACL,aAAIC,GACF,OAAOD,GAETE,KAvBY/L,IACZ,IAAK,MAAMgM,KAAYH,EACrBG,EAASD,MAAQC,EAASD,KAAK/L,IAsBjCiM,UAlBiBD,IACjBH,EAAWK,KAAKF,GACT,CACLG,YAAa,KACXN,EAAaA,EAAWpJ,OAAQ2J,GAAMA,IAAMJ,MAehDG,YAVkB,KAClBN,EAAa,MC/BjBQ,EAAgBrM,GACdI,EAASJ,KAAW8E,OAAOgD,KAAK9H,GAAOyD,OCHzC6I,EAAgBzM,GACG,SAAjBA,EAAQC,KCHVyM,EAAgBvM,GACG,mBAAVA,ECCTwM,EAAgBxM,IACd,IAAKkB,EACH,OAAO,EAGT,MAAMuL,EAAQzM,EAAUA,EAAsB0M,cAA6B,EAC3E,OACE1M,aACCyM,GAASA,EAAME,YAAcF,EAAME,YAAYvL,YAAcA,cCRlEwL,EAAgB/M,GACG,oBAAjBA,EAAQC,KCDV+M,GAAgBhN,GACG,UAAjBA,EAAQC,KCCVgN,GAAgB9C,GAAawC,EAAcxC,IAAQA,EAAI+C,YCsBzC,SAAUC,GAAMhK,EAAaC,GACzC,MAAMgK,EAAQ5M,MAAMC,QAAQ2C,GACxBA,EACAd,EAAMc,GACJ,CAACA,GACDN,EAAaM,GAEbiK,EAA+B,IAAjBD,EAAMxJ,OAAeT,EA3B3C,SAAiBA,EAAamK,GAC5B,MAAM1J,EAAS0J,EAAWC,MAAM,GAAG,GAAI3J,OACvC,IAAIF,EAAQ,EAEZ,KAAOA,EAAQE,GACbT,EAASX,EAAYW,GAAUO,IAAUP,EAAOmK,EAAW5J,MAG7D,OAAOP,CACT,CAkBoDqK,CAAQrK,EAAQiK,GAE5D1J,EAAQ0J,EAAMxJ,OAAS,EACvBvB,EAAM+K,EAAM1J,GAclB,OAZI2J,UACKA,EAAYhL,GAIT,IAAVqB,IACEnD,EAAS8M,IAAgBb,EAAca,IACtC7M,MAAMC,QAAQ4M,IA5BrB,SAAsB/B,GACpB,IAAK,MAAMjJ,KAAOiJ,EAChB,GAAIA,EAAInJ,eAAeE,KAASG,EAAY8I,EAAIjJ,IAC9C,OAAO,EAGX,OAAO,CACT,CAqBqCoL,CAAaJ,KAE9CF,GAAMhK,EAAQiK,EAAMG,MAAM,GAAG,IAGxBpK,CACT,CCjDA,IAAAuK,GAAmBhM,IACjB,IAAK,MAAMW,KAAOX,EAChB,GAAIgL,EAAWhL,EAAKW,IAClB,OAAO,EAGX,OAAO,GCDT,SAASsL,GAAmBjM,EAASkM,EAA8B,IACjE,MAAMC,EAAoBrN,MAAMC,QAAQiB,GAExC,GAAInB,EAASmB,IAASmM,EACpB,IAAK,MAAMxL,KAAOX,EAEdlB,MAAMC,QAAQiB,EAAKW,KAClB9B,EAASmB,EAAKW,MAAUqL,GAAkBhM,EAAKW,KAEhDuL,EAAOvL,GAAO7B,MAAMC,QAAQiB,EAAKW,IAAQ,GAAK,CAAA,EAC9CsL,GAAgBjM,EAAKW,GAAMuL,EAAOvL,KACxBhC,EAAkBqB,EAAKW,MACjCuL,EAAOvL,IAAO,GAKpB,OAAOuL,CACT,CAEA,SAASE,GACPpM,EACAwF,EACA6G,GAKA,MAAMF,EAAoBrN,MAAMC,QAAQiB,GAExC,GAAInB,EAASmB,IAASmM,EACpB,IAAK,MAAMxL,KAAOX,EAEdlB,MAAMC,QAAQiB,EAAKW,KAClB9B,EAASmB,EAAKW,MAAUqL,GAAkBhM,EAAKW,IAG9CG,EAAY0E,IACZO,EAAYsG,EAAsB1L,IAElC0L,EAAsB1L,GAAO7B,MAAMC,QAAQiB,EAAKW,IAC5CsL,GAAgBjM,EAAKW,GAAM,IAC3B,IAAKsL,GAAgBjM,EAAKW,KAE9ByL,GACEpM,EAAKW,GACLhC,EAAkB6G,GAAc,CAAA,EAAKA,EAAW7E,GAChD0L,EAAsB1L,IAI1B0L,EAAsB1L,IAAQqF,EAAUhG,EAAKW,GAAM6E,EAAW7E,IAKpE,OAAO0L,CACT,CAEA,IAAAC,GAAe,CAAIjJ,EAAkBmC,IACnC4G,GACE/I,EACAmC,EACAyG,GAAgBzG,IC/DpB,MAAM+G,GAAqC,CACzC9N,OAAO,EACPqG,SAAS,GAGL0H,GAAc,CAAE/N,OAAO,EAAMqG,SAAS,GAE5C,IAAA2H,GAAgBC,IACd,GAAI5N,MAAMC,QAAQ2N,GAAU,CAC1B,GAAIA,EAAQxK,OAAS,EAAG,CACtB,MAAMkF,EAASsF,EACZxL,OAAQyL,GAAWA,GAAUA,EAAOxN,UAAYwN,EAAO1I,UACvD2B,IAAK+G,GAAWA,EAAOlO,OAC1B,MAAO,CAAEA,MAAO2I,EAAQtC,UAAWsC,EAAOlF,QAG5C,OAAOwK,EAAQ,GAAGvN,UAAYuN,EAAQ,GAAGzI,SAErCyI,EAAQ,GAAGE,aAAe9L,EAAY4L,EAAQ,GAAGE,WAAWnO,OAC1DqC,EAAY4L,EAAQ,GAAGjO,QAA+B,KAArBiO,EAAQ,GAAGjO,MAC1C+N,GACA,CAAE/N,MAAOiO,EAAQ,GAAGjO,MAAOqG,SAAS,GACtC0H,GACFD,GAGN,OAAOA,IC7BTM,GAAe,CACbpO,GACEqO,gBAAeC,cAAaC,gBAE9BlM,EAAYrC,GACRA,EACAqO,EACY,KAAVrO,EACEwO,IACAxO,GACGA,EACDA,EACJsO,GAAe1H,EAAS5G,GACtB,IAAIC,KAAKD,GACTuO,EACEA,EAAWvO,GACXA,ECfZ,MAAMyO,GAAkC,CACtCpI,SAAS,EACTrG,MAAO,MAGT,IAAA0O,GAAgBT,GACd5N,MAAMC,QAAQ2N,GACVA,EAAQ7K,OACN,CAACuL,EAAUT,IACTA,GAAUA,EAAOxN,UAAYwN,EAAO1I,SAChC,CACEa,SAAS,EACTrG,MAAOkO,EAAOlO,OAEhB2O,EACNF,IAEFA,GCXQ,SAAUG,GAAcxE,GACpC,MAAMJ,EAAMI,EAAGJ,IAEf,OAAIsC,EAAYtC,GACPA,EAAI6E,MAGThC,GAAa7C,GACR0E,GAActE,EAAG0E,MAAM9O,MAG5B4M,EAAiB5C,GACZ,IAAIA,EAAI+E,iBAAiB5H,IAAI,EAAGnH,WAAYA,GAGjDgP,EAAWhF,GACNgE,GAAiB5D,EAAG0E,MAAM9O,MAG5BoO,GAAgB/L,EAAY2H,EAAIhK,OAASoK,EAAGJ,IAAIhK,MAAQgK,EAAIhK,MAAOoK,EAC5E,CCpBA,ICXA6E,GAAgBjP,GAAoCA,aAAiBkP,OCSrEC,GACEC,GAEA/M,EAAY+M,GACRA,EACAH,GAAQG,GACNA,EAAKC,OACLjP,EAASgP,GACPH,GAAQG,EAAKpP,OACXoP,EAAKpP,MAAMqP,OACXD,EAAKpP,MACPoP,ECjBVE,GAAgBC,IAAW,CACzBC,YAAaD,GAAQA,IAASxL,EAC9B0L,SAAUF,IAASxL,EACnB2L,WAAYH,IAASxL,EACrB4L,QAASJ,IAASxL,EAClB6L,UAAWL,IAASxL,ICJtB,MAAM8L,GAAiB,gBAEvB,IAAAC,GAAgBC,KACZA,KACAA,EAAeC,aAEdzD,EAAWwD,EAAeC,WACzBD,EAAeC,SAASlO,YAAYjB,OAASgP,IAC9CzP,EAAS2P,EAAeC,WACvBlL,OAAO6D,OAAOoH,EAAeC,UAAUC,KACpCC,GACCA,EAAiBpO,YAAYjB,OAASgP,KCbhDM,GAAe,CACbtP,EACAiG,EACAsJ,KAECA,IACAtJ,EAAOO,UACNP,EAAOG,MAAMnG,IAAID,IACjB,IAAIiG,EAAOG,OAAOoJ,KACfC,GACCzP,EAAK0P,WAAWD,IAChB,SAASlO,KAAKvB,EAAKuM,MAAMkD,EAAU7M,WCT3C,MAAM+M,GAAwB,CAC5B/C,EACA1C,EACA0F,EACAC,KAEA,IAAK,MAAMxO,KAAOuO,GAAe3L,OAAOgD,KAAK2F,GAAS,CACpD,MAAMvD,EAAQnH,EAAI0K,EAAQvL,GAE1B,GAAIgI,EAAO,CACT,MAAME,GAAEA,KAAOuG,GAAiBzG,EAEhC,GAAIE,EAAI,CACN,GAAIA,EAAG0E,MAAQ1E,EAAG0E,KAAK,IAAM/D,EAAOX,EAAG0E,KAAK,GAAI5M,KAASwO,EACvD,OAAO,EACF,GAAItG,EAAGJ,KAAOe,EAAOX,EAAGJ,IAAKI,EAAGvJ,QAAU6P,EAC/C,OAAO,EAEP,GAAIF,GAAsBG,EAAc5F,GACtC,WAGC,GAAI3K,EAASuQ,IACdH,GAAsBG,EAA2B5F,GACnD,SCxBI,SAAU6F,GACtBtK,EACA6D,EACAtJ,GAKA,MAAM+I,EAAQ7G,EAAIuD,EAAQzF,GAE1B,GAAI+I,GAASzH,EAAMtB,GACjB,MAAO,CACL+I,QACA/I,QAIJ,MAAMD,EAAQC,EAAKiC,MAAM,KAEzB,KAAOlC,EAAM6C,QAAQ,CACnB,MAAM2D,EAAYxG,EAAMiQ,KAAK,KACvB3G,EAAQnH,EAAIoH,EAAS/C,GACrB0J,EAAa/N,EAAIuD,EAAQc,GAE/B,GAAI8C,IAAU7J,MAAMC,QAAQ4J,IAAUrJ,IAASuG,EAC7C,MAAO,CAAEvG,QAGX,GAAIiQ,GAAcA,EAAWhR,KAC3B,MAAO,CACLe,KAAMuG,EACNwC,MAAOkH,GAIX,GAAIA,GAAcA,EAAWC,MAAQD,EAAWC,KAAKjR,KACnD,MAAO,CACLe,KAAM,GAAGuG,SACTwC,MAAOkH,EAAWC,MAItBnQ,EAAMoQ,MAGR,MAAO,CACLnQ,OAEJ,CC3CA,ICCAoQ,GAAe,CACb3K,EACAsD,EACA/I,KAEA,MAAMqQ,EAAmBvF,EAAsB5I,EAAIuD,EAAQzF,IAG3D,OAFAyC,EAAI4N,EAAkB,OAAQtH,EAAM/I,IACpCyC,EAAIgD,EAAQzF,EAAMqQ,GACX5K,GCfT6K,GAAgBnR,GAAqC4G,EAAS5G,GCChD,SAAUoR,GACtBjO,EACA6G,EACAlK,EAAO,YAEP,GACEqR,GAAUhO,IACT9C,MAAMC,QAAQ6C,IAAWA,EAAOkO,MAAMF,KACtC9N,EAAUF,KAAYA,EAEvB,MAAO,CACLrD,OACA0K,QAAS2G,GAAUhO,GAAUA,EAAS,GACtC6G,MAGN,CChBA,IAAAsH,GAAgBC,GACdnR,EAASmR,KAAoBtC,GAAQsC,GACjCA,EACA,CACEvR,MAAOuR,EACP/G,QAAS,ICwBjBgH,GAAeC,MACbvH,EACAwH,EACA3K,EACA0E,EACAkG,EACAC,KAEA,MAAM5H,IACJA,EAAG8E,KACHA,EAAI+C,SACJA,EAAQC,UACRA,EAASC,UACTA,EAASC,IACTA,EAAGC,IACHA,EAAGC,QACHA,EAAOlC,SACPA,EAAQnP,KACRA,EAAIwN,cACJA,EAAaxD,MACbA,GACEX,EAAME,GACJ+H,EAA+BpP,EAAIgE,EAAYlG,GACrD,IAAKgK,GAAS6G,EAAmB5Q,IAAID,GACnC,MAAO,CAAA,EAET,MAAMuR,EAA6BtD,EAAOA,EAAK,GAAM9E,EAC/CO,EAAqBC,IACrBmH,GAA6BS,EAAS3H,iBACxC2H,EAAS7H,kBAAkBlH,EAAUmH,GAAW,GAAKA,GAAW,IAChE4H,EAAS3H,mBAGPb,EAA6B,CAAA,EAC7ByI,EAAUxF,GAAa7C,GACvBgF,EAAapP,EAAgBoK,GAC7BsI,EAAoBD,GAAWrD,EAC/BuD,GACFlE,GAAiB/B,EAAYtC,KAC7B3H,EAAY2H,EAAIhK,QAChBqC,EAAY8P,IACb3F,EAAcxC,IAAsB,KAAdA,EAAIhK,OACZ,KAAfmS,GACC9R,MAAMC,QAAQ6R,KAAgBA,EAAW1O,OACtC+O,EAAoBhH,EAAaiH,KACrC,KACA5R,EACA4K,EACA7B,GAEI8I,EAAmB,CACvBC,EACAC,EACAC,EACAC,EAAmB9O,EACnB+O,EAAmB/O,KAEnB,MAAMwG,EAAUmI,EAAYC,EAAmBC,EAC/CjJ,EAAM/I,GAAQ,CACZf,KAAM6S,EAAYG,EAAUC,EAC5BvI,UACAR,SACGwI,EAAkBG,EAAYG,EAAUC,EAASvI,KAIxD,GACEoH,GACKvR,MAAMC,QAAQ6R,KAAgBA,EAAW1O,OAC1CoO,KACGS,IAAsBC,GAAWrS,EAAkBiS,KACnD9O,EAAU8O,KAAgBA,GAC1BnD,IAAehB,GAAiBc,GAAMzI,SACtCgM,IAAY3D,GAAcI,GAAMzI,SACvC,CACA,MAAMrG,MAAEA,EAAKwK,QAAEA,GAAY2G,GAAUU,GACjC,CAAE7R,QAAS6R,EAAUrH,QAASqH,GAC9BP,GAAmBO,GAEvB,GAAI7R,IACF4J,EAAM/I,GAAQ,CACZf,KAAMkE,EACNwG,UACAR,IAAKoI,KACFI,EAAkBxO,EAAiCwG,KAEnDiB,GAEH,OADAlB,EAAkBC,GACXZ,EAKb,KAAK2I,GAAarS,EAAkB8R,IAAS9R,EAAkB+R,IAAO,CACpE,IAAIU,EACAK,EACJ,MAAMC,EAAY3B,GAAmBW,GAC/BiB,EAAY5B,GAAmBU,GAErC,GAAK9R,EAAkBiS,IAAgBtO,MAAMsO,GAUtC,CACL,MAAMgB,EACHnJ,EAAyBsE,aAAe,IAAIrO,KAAKkS,GAC9CiB,EAAqBC,GACzB,IAAIpT,MAAK,IAAIA,MAAOqT,eAAiB,IAAMD,GACvCE,EAAqB,QAAZvJ,EAAIlK,KACb0T,EAAqB,QAAZxJ,EAAIlK,KAEf8G,EAASqM,EAAUjT,QAAUmS,IAC/BQ,EAAYY,EACRH,EAAkBjB,GAAciB,EAAkBH,EAAUjT,OAC5DwT,EACErB,EAAac,EAAUjT,MACvBmT,EAAY,IAAIlT,KAAKgT,EAAUjT,QAGnC4G,EAASsM,EAAUlT,QAAUmS,IAC/Ba,EAAYO,EACRH,EAAkBjB,GAAciB,EAAkBF,EAAUlT,OAC5DwT,EACErB,EAAae,EAAUlT,MACvBmT,EAAY,IAAIlT,KAAKiT,EAAUlT,YA/B2B,CAClE,MAAMyT,EACHzJ,EAAyBqE,gBACzB8D,GAAcA,EAAaA,GACzBjS,EAAkB+S,EAAUjT,SAC/B2S,EAAYc,EAAcR,EAAUjT,OAEjCE,EAAkBgT,EAAUlT,SAC/BgT,EAAYS,EAAcP,EAAUlT,OA2BxC,IAAI2S,GAAaK,KACfN,IACIC,EACFM,EAAUzI,QACV0I,EAAU1I,QACVxG,EACAA,IAEGyH,GAEH,OADAlB,EAAkBX,EAAM/I,GAAO2J,SACxBZ,EAKb,IACGkI,GAAaC,KACbQ,IACA3L,EAASuL,IAAgBP,GAAgBvR,MAAMC,QAAQ6R,IACxD,CACA,MAAMuB,EAAkBpC,GAAmBQ,GACrC6B,EAAkBrC,GAAmBS,GACrCY,GACHzS,EAAkBwT,EAAgB1T,QACnCmS,EAAW1O,QAAUiQ,EAAgB1T,MACjCgT,GACH9S,EAAkByT,EAAgB3T,QACnCmS,EAAW1O,QAAUkQ,EAAgB3T,MAEvC,IAAI2S,GAAaK,KACfN,EACEC,EACAe,EAAgBlJ,QAChBmJ,EAAgBnJ,UAEbiB,GAEH,OADAlB,EAAkBX,EAAM/I,GAAO2J,SACxBZ,EAKb,GAAIsI,IAAYK,GAAW3L,EAASuL,GAAa,CAC/C,MAAQnS,MAAO4T,EAAYpJ,QAAEA,GAAY8G,GAAmBY,GAE5D,GAAIjD,GAAQ2E,KAAkBzB,EAAW0B,MAAMD,KAC7ChK,EAAM/I,GAAQ,CACZf,KAAMkE,EACNwG,UACAR,SACGwI,EAAkBxO,EAAgCwG,KAElDiB,GAEH,OADAlB,EAAkBC,GACXZ,EAKb,GAAIoG,EACF,GAAIzD,EAAWyD,GAAW,CACxB,MACM8D,EAAgB1C,SADDpB,EAASmC,EAAYpL,GACKqL,GAE/C,GAAI0B,IACFlK,EAAM/I,GAAQ,IACTiT,KACAtB,EACDxO,EACA8P,EAActJ,WAGbiB,GAEH,OADAlB,EAAkBuJ,EAActJ,SACzBZ,OAGN,GAAIxJ,EAAS4P,GAAW,CAC7B,IAAI+D,EAAmB,CAAA,EAEvB,IAAK,MAAM7R,KAAO8N,EAAU,CAC1B,IAAK3D,EAAc0H,KAAsBtI,EACvC,MAGF,MAAMqI,EAAgB1C,SACdpB,EAAS9N,GAAKiQ,EAAYpL,GAChCqL,EACAlQ,GAGE4R,IACFC,EAAmB,IACdD,KACAtB,EAAkBtQ,EAAK4R,EAActJ,UAG1CD,EAAkBuJ,EAActJ,SAE5BiB,IACF7B,EAAM/I,GAAQkT,IAKpB,IAAK1H,EAAc0H,KACjBnK,EAAM/I,GAAQ,CACZmJ,IAAKoI,KACF2B,IAEAtI,GACH,OAAO7B,EAOf,OADAW,GAAkB,GACXX,GCnMT,MAAMoK,GAAiB,CACrBzE,KAAMxL,EACNkQ,eAAgBlQ,EAChBmQ,kBAAkB,GAGd,SAAUC,GAKd7O,EAAkE,IAUlE,IAwCI8O,EAxCAzJ,EAAW,IACVqJ,MACA1O,GAEDM,EAAsC,CACxCyO,YAAa,EACbtO,SAAS,EACTuO,SAAS,EACTtO,UAAWuG,EAAW5B,EAAS/F,eAC/BwB,cAAc,EACdmO,aAAa,EACbC,cAAc,EACdC,oBAAoB,EACpBpO,SAAS,EACTH,cAAe,CAAA,EACfD,YAAa,CAAA,EACbE,iBAAkB,CAAA,EAClBG,OAAQqE,EAASrE,QAAU,CAAA,EAC3Bd,SAAUmF,EAASnF,WAAY,GAE7B2E,EAAqB,CAAA,EACrBtF,GACFzE,EAASuK,EAAS/F,gBAAkBxE,EAASuK,EAAShC,UAClDrH,EAAYqJ,EAAS/F,eAAiB+F,EAAShC,SAC/C,CAAA,EACFC,EAAc+B,EAAS3B,iBACtB,CAAA,EACA1H,EAAYuD,GACbiG,EAAS,CACXC,QAAQ,EACRF,OAAO,EACP5D,OAAO,GAELH,EAAgB,CAClB+D,MAAO,IAAI6J,IACXlP,SAAU,IAAIkP,IACdC,QAAS,IAAID,IACbxL,MAAO,IAAIwL,IACXzN,MAAO,IAAIyN,KAGTE,EAAQ,EACZ,MAAM3P,EAAiC,CACrCc,SAAS,EACTE,aAAa,EACbE,kBAAkB,EAClBD,eAAe,EACfE,cAAc,EACdC,SAAS,EACTC,QAAQ,GAEV,IAAIuO,EAA2B,IAC1B5P,GAEL,MAAM6P,EAAoC,CACxC5L,MAAO0C,IACPmJ,MAAOnJ,KAGHoJ,EACJrK,EAASsK,eAAiBlR,EAStB2C,EAAY+K,MAAOyD,IACvB,IACGvK,EAASnF,WACTP,EAAgBoB,SACfwO,EAAyBxO,SACzB6O,GACF,CACA,MAAM7O,EAAUsE,EAASwK,SACrB9I,SAAqB+I,KAAc9O,cAC7B+O,EAAyBlL,GAAS,GAExC9D,IAAYT,EAAWS,SACzByO,EAAUC,MAAMhJ,KAAK,CACnB1F,cAMFiP,EAAsB,CAAC1U,EAAkBwF,MAE1CuE,EAASnF,WACTP,EAAgBmB,cACfnB,EAAgBkB,kBAChB0O,EAAyBzO,cACzByO,EAAyB1O,qBAE1BvF,GAASP,MAAMkV,KAAKzO,EAAO+D,QAAQ2K,QAAS3U,IACvCA,IACFuF,EACI9C,EAAIsC,EAAWO,iBAAkBtF,EAAMuF,GACvC4G,GAAMpH,EAAWO,iBAAkBtF,MAI3CiU,EAAUC,MAAMhJ,KAAK,CACnB5F,iBAAkBP,EAAWO,iBAC7BC,cAAeiG,EAAczG,EAAWO,sBA8ExCsP,EAAsB,CAC1B5U,EACA6U,EACA1V,EACAgK,KAEA,MAAME,EAAenH,EAAIoH,EAAStJ,GAElC,GAAIqJ,EAAO,CACT,MAAMhH,EAAeH,EACnB6F,EACA/H,EACAwB,EAAYrC,GAAS+C,EAAI8B,EAAgBhE,GAAQb,GAGnDqC,EAAYa,IACX8G,GAAQA,EAAyB2L,gBAClCD,EACIpS,EACEsF,EACA/H,EACA6U,EAAuBxS,EAAe0L,GAAc1E,EAAME,KAE5DwL,EAAc/U,EAAMqC,GAExB4H,EAAOD,OAASnE,MAIdmP,EAAsB,CAC1BhV,EACAiV,EACA1F,EACA2F,EACAC,KAIA,IAAIC,GAAoB,EACpBC,GAAkB,EACtB,MAAM9K,EAA8D,CAClEvK,QAGF,IAAK8J,EAASnF,SAAU,CACtB,IAAK4K,GAAe2F,EAAa,EAC3B9Q,EAAgBc,SAAW8O,EAAyB9O,WACtDmQ,EAAkBtQ,EAAWG,QAC7BH,EAAWG,QAAUqF,EAAOrF,QAAUoQ,IACtCF,EAAoBC,IAAoB9K,EAAOrF,SAGjD,MAAMqQ,EAAyB7O,EAC7BxE,EAAI8B,EAAgBhE,GACpBiV,GAGFI,IAAoBnT,EAAI6C,EAAWK,YAAapF,GAChDuV,EACIpJ,GAAMpH,EAAWK,YAAapF,GAC9ByC,EAAIsC,EAAWK,YAAapF,GAAM,GACtCuK,EAAOnF,YAAcL,EAAWK,YAChCgQ,EACEA,IACEhR,EAAgBgB,aAChB4O,EAAyB5O,cACzBiQ,KAAqBE,EAG3B,GAAIhG,EAAa,CACf,MAAMiG,EAAyBtT,EAAI6C,EAAWM,cAAerF,GAExDwV,IACH/S,EAAIsC,EAAWM,cAAerF,EAAMuP,GACpChF,EAAOlF,cAAgBN,EAAWM,cAClC+P,EACEA,IACEhR,EAAgBiB,eAChB2O,EAAyB3O,gBACzBmQ,IAA2BjG,GAInC6F,GAAqBD,GAAgBlB,EAAUC,MAAMhJ,KAAKX,GAG5D,OAAO6K,EAAoB7K,EAAS,CAAA,GAGhCkL,EAAsB,CAC1BzV,EACAwF,EACAuD,EACAL,KAMA,MAAMgN,EAAqBxT,EAAI6C,EAAWU,OAAQzF,GAC5CqU,GACHjQ,EAAgBoB,SAAWwO,EAAyBxO,UACrDhD,EAAUgD,IACVT,EAAWS,UAAYA,EAhOzB,IAAqBI,EA6OrB,GAXIkE,EAAS6L,YAAc5M,GAlONnD,EAmOW,IAzHb,EAAC5F,EAAyB+I,KAC7CtG,EAAIsC,EAAWU,OAAQzF,EAAM+I,GAC7BkL,EAAUC,MAAMhJ,KAAK,CACnBzF,OAAQV,EAAWU,UAsHiBmQ,CAAa5V,EAAM+I,GAAvDwK,EAlODsC,IACCC,aAAa/B,GACbA,EAAQgC,WAAWnQ,EAAUiQ,IAiO7BtC,EAAmBzJ,EAAS6L,cAE5BG,aAAa/B,GACbR,EAAqB,KACrBxK,EACItG,EAAIsC,EAAWU,OAAQzF,EAAM+I,GAC7BoD,GAAMpH,EAAWU,OAAQzF,KAI5B+I,GAASrC,EAAUgP,EAAoB3M,GAAS2M,KAChDlK,EAAc9C,IACf2L,EACA,CACA,MAAM2B,EAAmB,IACpBtN,KACC2L,GAAqB7R,EAAUgD,GAAW,CAAEA,WAAY,GAC5DC,OAAQV,EAAWU,OACnBzF,QAGF+E,EAAa,IACRA,KACAiR,GAGL/B,EAAUC,MAAMhJ,KAAK8K,KAInBzB,EAAa3D,MAAO5Q,IACxByU,EAAoBzU,GAAM,GAC1B,MAAMsC,QAAewH,EAASwK,SAC5BvM,EACA+B,EAASmM,QdzaA,EACbrG,EACAtG,EACA8K,EACAtD,KAEA,MAAMlE,EAAiD,CAAA,EAEvD,IAAK,MAAM5M,KAAQ4P,EAAa,CAC9B,MAAMvG,EAAenH,EAAIoH,EAAStJ,GAElCqJ,GAAS5G,EAAImK,EAAQ5M,EAAMqJ,EAAME,IAGnC,MAAO,CACL6K,eACArU,MAAO,IAAI6P,GACXhD,SACAkE,8BcwZEoF,CACElW,GAAQiG,EAAO+D,MACfV,EACAQ,EAASsK,aACTtK,EAASgH,4BAIb,OADA2D,EAAoBzU,GACbsC,GAoBHkS,EAA2B5D,MAC/BhE,EACAuJ,EACAF,EAEI,CACFG,OAAO,MAGT,IAAK,MAAMpW,KAAQ4M,EAAQ,CACzB,MAAMvD,EAAQuD,EAAO5M,GAErB,GAAIqJ,EAAO,CACT,MAAME,GAAEA,KAAO0L,GAAe5L,EAE9B,GAAIE,EAAI,CACN,MAAM8M,EAAmBpQ,EAAOoC,MAAMpI,IAAIsJ,EAAGvJ,MACvCsW,EACJjN,EAAME,IAAM0F,GAAsB5F,EAAgBE,IAEhD+M,GAAqBlS,EAAgBkB,kBACvCmP,EAAoB,CAACzU,IAAO,GAG9B,MAAMuW,QAAmB5F,GACvBtH,EACApD,EAAOtB,SACPoD,EACAoM,EACArK,EAASgH,4BAA8BqF,EACvCE,GAOF,GAJIC,GAAqBlS,EAAgBkB,kBACvCmP,EAAoB,CAACzU,IAGnBuW,EAAWhN,EAAGvJ,QAChBiW,EAAQG,OAAQ,EACZD,GACF,OAIHA,IACEjU,EAAIqU,EAAYhN,EAAGvJ,MAChBqW,EACEjG,GACErL,EAAWU,OACX8Q,EACAhN,EAAGvJ,MAELyC,EAAIsC,EAAWU,OAAQ8D,EAAGvJ,KAAMuW,EAAWhN,EAAGvJ,OAChDmM,GAAMpH,EAAWU,OAAQ8D,EAAGvJ,QAGnCwL,EAAcyJ,UACNT,EACLS,EACAkB,EACAF,IAKR,OAAOA,EAAQG,OAiBXd,EAAwB,CAACtV,EAAMU,KAClCoJ,EAASnF,WACT3E,GAAQU,GAAQ+B,EAAIsF,EAAa/H,EAAMU,IACvCgG,EAAU8P,KAAaxS,IAEpB4D,EAAyC,CAC7C7H,EACAsC,EACA8D,IAEAH,EACEjG,EACAkG,EACA,IACMgE,EAAOD,MACPjC,EACAvG,EAAYa,GACV2B,EACA+B,EAAShG,GACP,CAAEA,CAACA,GAAQsC,GACXA,GAEV8D,EACA9D,GAcE0S,EAAgB,CACpB/U,EACAb,EACAiO,EAA0B,CAAA,KAE1B,MAAM/D,EAAenH,EAAIoH,EAAStJ,GAClC,IAAIiV,EAAsB9V,EAE1B,GAAIkK,EAAO,CACT,MAAM6F,EAAiB7F,EAAME,GAEzB2F,KACDA,EAAevK,UACdlC,EAAIsF,EAAa/H,EAAMuN,GAAgBpO,EAAO+P,IAEhD+F,EACEtJ,EAAcuD,EAAe/F,MAAQ9J,EAAkBF,GACnD,GACAA,EAEF4M,EAAiBmD,EAAe/F,KAClC,IAAI+F,EAAe/F,IAAIiE,SAASuH,QAC7B8B,GACEA,EAAUC,SACTzB,EACA7N,SAASqP,EAAUtX,QAEhB+P,EAAejB,KACpBlP,EAAgBmQ,EAAe/F,KACjC+F,EAAejB,KAAK0G,QAASgC,IACtBA,EAAY7B,gBAAmB6B,EAAYhS,WAC1CnF,MAAMC,QAAQwV,GAChB0B,EAAY9W,UAAYoV,EAAW7F,KAChC1O,GAAiBA,IAASiW,EAAYxX,OAGzCwX,EAAY9W,QACVoV,IAAe0B,EAAYxX,SAAW8V,KAK9C/F,EAAejB,KAAK0G,QACjBiC,GACEA,EAAS/W,QAAU+W,EAASzX,QAAU8V,GAGpCxJ,EAAYyD,EAAe/F,KACpC+F,EAAe/F,IAAIhK,MAAQ,IAE3B+P,EAAe/F,IAAIhK,MAAQ8V,EAEtB/F,EAAe/F,IAAIlK,MACtBgV,EAAUC,MAAMhJ,KAAK,CACnBlL,OACA8H,OAAQrH,EAAYsH,QAO7BqF,EAAQ8H,aAAe9H,EAAQyJ,cAC9B7B,EACEhV,EACAiV,EACA7H,EAAQyJ,YACRzJ,EAAQ8H,aACR,GAGJ9H,EAAQ0J,gBAAkBC,GAAQ/W,IAG9BgX,EAAY,CAKhBhX,EACAb,EACAiO,KAEA,IAAK,MAAM6J,KAAY9X,EAAO,CAC5B,IAAKA,EAAMgC,eAAe8V,GACxB,OAEF,MAAMhC,EAAa9V,EAAM8X,GACnB1Q,EAAYvG,EAAO,IAAMiX,EACzB5N,EAAQnH,EAAIoH,EAAS/C,IAE1BN,EAAOoC,MAAMpI,IAAID,IAChBT,EAAS0V,IACR5L,IAAUA,EAAME,MAClBrK,EAAa+V,GACV+B,EAAUzQ,EAAW0O,EAAY7H,GACjC2H,EAAcxO,EAAW0O,EAAY7H,KAIvC8J,EAA0C,CAC9ClX,EACAb,EACAiO,EAAU,CAAA,KAEV,MAAM/D,EAAQnH,EAAIoH,EAAStJ,GACrB+Q,EAAe9K,EAAOoC,MAAMpI,IAAID,GAChCmX,EAAa1W,EAAYtB,GAE/BsD,EAAIsF,EAAa/H,EAAMmX,GAEnBpG,GACFkD,EAAU5L,MAAM6C,KAAK,CACnBlL,OACA8H,OAAQrH,EAAYsH,MAInB3D,EAAgBc,SACfd,EAAgBgB,aAChB4O,EAAyB9O,SACzB8O,EAAyB5O,cAC3BgI,EAAQ8H,aAERjB,EAAUC,MAAMhJ,KAAK,CACnBlL,OACAoF,YAAa4H,GAAehJ,EAAgB+D,GAC5C7C,QAASoQ,EAAUtV,EAAMmX,OAI7B9N,GAAUA,EAAME,IAAOlK,EAAkB8X,GAErCpC,EAAc/U,EAAMmX,EAAY/J,GADhC4J,EAAUhX,EAAMmX,EAAY/J,GAIlCkC,GAAUtP,EAAMiG,IAAWgO,EAAUC,MAAMhJ,KAAK,IAAKnG,EAAY/E,SACjEiU,EAAUC,MAAMhJ,KAAK,CACnBlL,KAAMiK,EAAOD,MAAQhK,OAAO0B,EAC5BoG,OAAQrH,EAAYsH,MAIlBiB,EAA0B4H,MAAOjR,IACrCsK,EAAOD,OAAQ,EACf,MAAMpK,EAASD,EAAMC,OACrB,IAAII,EAAeJ,EAAOI,KACtBoX,GAAsB,EAC1B,MAAM/N,EAAenH,EAAIoH,EAAStJ,GAC5BqX,EAA8BpC,IAClCmC,EACEE,OAAOtU,MAAMiS,IACZ/V,EAAa+V,IAAejS,MAAMiS,EAAWlO,YAC9CL,EAAUuO,EAAY/S,EAAI6F,EAAa/H,EAAMiV,KAE3CsC,EAA6B9I,GAAmB3E,EAAS4E,MACzD8I,EAA4B/I,GAChC3E,EAASsJ,gBAGX,GAAI/J,EAAO,CACT,IAAIN,EACAvD,EACJ,MAAMyP,EAAarV,EAAOX,KACtB8O,GAAc1E,EAAME,IACpB7J,EAAcC,GACZ4P,EACJ5P,EAAMV,OAASgE,GAAetD,EAAMV,OAASgE,EACzCwU,KC9uBIrK,ED+uBQ/D,EAAME,IC9uBpBS,QACPoD,EAAQ4D,UACP5D,EAAQ+D,KACR/D,EAAQgE,KACRhE,EAAQ6D,WACR7D,EAAQ8D,WACR9D,EAAQiE,SACRjE,EAAQ+B,WDwuBDrF,EAASwK,UACTpS,EAAI6C,EAAWU,OAAQzF,IACvBqJ,EAAME,GAAGmO,OElvBL,EACbnI,EACAzG,EACA4K,EACAN,EAIA1E,KAEIA,EAAKI,WAEG4E,GAAehF,EAAKK,YACrBjG,GAAayG,IACbmE,EAAcN,EAAexE,SAAWF,EAAKE,WAC9CW,IACCmE,EAAcN,EAAevE,WAAaH,EAAKG,aACjDU,GFkuBHoI,CACEpI,EACArN,EAAI6C,EAAWM,cAAerF,GAC9B+E,EAAW2O,YACX8D,EACAD,GAEEK,EAAUtI,GAAUtP,EAAMiG,EAAQsJ,GAExC9M,EAAIsF,EAAa/H,EAAMiV,GAEnB1F,GACFlG,EAAME,GAAGL,QAAUG,EAAME,GAAGL,OAAOvJ,GACnC4T,GAAsBA,EAAmB,IAChClK,EAAME,GAAGP,UAClBK,EAAME,GAAGP,SAASrJ,GAGpB,MAAM+I,EAAasM,EAAoBhV,EAAMiV,EAAY1F,GAEnD4F,GAAgB3J,EAAc9C,IAAekP,EASnD,IAPCrI,GACC0E,EAAUC,MAAMhJ,KAAK,CACnBlL,OACAf,KAAMU,EAAMV,KACZ6I,OAAQrH,EAAYsH,KAGpB0P,EAWF,OAVIrT,EAAgBoB,SAAWwO,EAAyBxO,WAChC,WAAlBsE,EAAS4E,KACPa,GACF1J,IAEQ0J,GACV1J,KAKFsP,GACAlB,EAAUC,MAAMhJ,KAAK,CAAElL,UAAU4X,EAAU,CAAA,EAAKlP,IAMpD,IAFC6G,GAAeqI,GAAW3D,EAAUC,MAAMhJ,KAAK,IAAKnG,IAEjD+E,EAASwK,SAAU,CACrB,MAAM7O,OAAEA,SAAiB8O,EAAW,CAACvU,IAIrC,GAFAqX,EAA2BpC,GAEvBmC,EAAqB,CACvB,MAAMS,EAA4B9H,GAChChL,EAAWU,OACX6D,EACAtJ,GAEI8X,EAAoB/H,GACxBtK,EACA6D,EACAuO,EAA0B7X,MAAQA,GAGpC+I,EAAQ+O,EAAkB/O,MAC1B/I,EAAO8X,EAAkB9X,KAEzBwF,EAAUgG,EAAc/F,SAG1BgP,EAAoB,CAACzU,IAAO,GAC5B+I,SACQ4H,GACJtH,EACApD,EAAOtB,SACPoD,EACAoM,EACArK,EAASgH,4BAEX9Q,GACFyU,EAAoB,CAACzU,IAErBqX,EAA2BpC,GAEvBmC,IACErO,EACFvD,GAAU,GAEVpB,EAAgBoB,SAChBwO,EAAyBxO,WAEzBA,QAAgBgP,EAAyBlL,GAAS,KAKpD8N,IACF/N,EAAME,GAAGmO,MACPX,GACE1N,EAAME,GAAGmO,MAIbjC,EAAoBzV,EAAMwF,EAASuD,EAAOL,IC31BnC,IAAC0E,GDg2BR2K,EAAc,CAAC5O,EAAU9H,KAC7B,GAAIa,EAAI6C,EAAWU,OAAQpE,IAAQ8H,EAAIK,MAErC,OADAL,EAAIK,QACG,GAKLuN,GAAwCnG,MAAO5Q,EAAMoN,EAAU,CAAA,KACnE,IAAI5H,EACA0N,EACJ,MAAM8E,EAAalN,EAAsB9K,GAEzC,GAAI8J,EAASwK,SAAU,CACrB,MAAM7O,OAhb0BmL,OAAO7Q,IACzC,MAAM0F,OAAEA,SAAiB8O,EAAWxU,GAEpC,GAAIA,EACF,IAAK,MAAMC,KAAQD,EAAO,CACxB,MAAMgJ,EAAQ7G,EAAIuD,EAAQzF,GAC1B+I,EACItG,EAAIsC,EAAWU,OAAQzF,EAAM+I,GAC7BoD,GAAMpH,EAAWU,OAAQzF,QAG/B+E,EAAWU,OAASA,EAGtB,OAAOA,GAkagBwS,CACnBzW,EAAYxB,GAAQA,EAAOgY,GAG7BxS,EAAUgG,EAAc/F,GACxByN,EAAmBlT,GACdgY,EAAWxI,KAAMxP,GAASkC,EAAIuD,EAAQzF,IACvCwF,OACKxF,GACTkT,SACQgF,QAAQC,IACZH,EAAW1R,IAAIsK,MAAOrK,IACpB,MAAM8C,EAAQnH,EAAIoH,EAAS/C,GAC3B,aAAaiO,EACXnL,GAASA,EAAME,GAAK,CAAEhD,CAACA,GAAY8C,GAAUA,OAInDmH,MAAM3O,UACLqR,GAAqBnO,EAAWS,UAAYK,KAE/CqN,EAAmB1N,QAAgBgP,EAAyBlL,GAqB9D,OAlBA2K,EAAUC,MAAMhJ,KAAK,KACdnF,EAAS/F,KACZoE,EAAgBoB,SAAWwO,EAAyBxO,UACpDA,IAAYT,EAAWS,QACrB,CAAA,EACA,CAAExF,WACF8J,EAASwK,WAAatU,EAAO,CAAEwF,WAAY,GAC/CC,OAAQV,EAAWU,SAGrB2H,EAAQgL,cACLlF,GACDvD,GACErG,EACAyO,EACA/X,EAAOgY,EAAa/R,EAAO+D,OAGxBkJ,GAGHsD,GACJwB,IAIA,MAAMlQ,EAAS,IACTmC,EAAOD,MAAQjC,EAAc/D,GAGnC,OAAOxC,EAAYwW,GACflQ,EACA/B,EAASiS,GACP9V,EAAI4F,EAAQkQ,GACZA,EAAW1R,IAAKtG,GAASkC,EAAI4F,EAAQ9H,KAGvCqY,GAAoD,CACxDrY,EACA2D,KAAS,CAETiF,UAAW1G,GAAKyB,GAAaoB,GAAYU,OAAQzF,GACjDkF,UAAWhD,GAAKyB,GAAaoB,GAAYK,YAAapF,GACtD+I,MAAO7G,GAAKyB,GAAaoB,GAAYU,OAAQzF,GAC7CuF,eAAgBrD,EAAI6C,EAAWO,iBAAkBtF,GACjD8I,YAAa5G,GAAKyB,GAAaoB,GAAYM,cAAerF,KActDsY,GAA0C,CAACtY,EAAM+I,EAAOqE,KAC5D,MAAMjE,GAAOjH,EAAIoH,EAAStJ,EAAM,CAAEuJ,GAAI,KAAMA,IAAM,CAAA,GAAIJ,IAChDoP,EAAerW,EAAI6C,EAAWU,OAAQzF,IAAS,CAAA,GAG7CmJ,IAAKqP,EAAU7O,QAAEA,EAAO1K,KAAEA,KAASwZ,GAAoBF,EAE/D9V,EAAIsC,EAAWU,OAAQzF,EAAM,IACxByY,KACA1P,EACHI,QAGF8K,EAAUC,MAAMhJ,KAAK,CACnBlL,OACAyF,OAAQV,EAAWU,OACnBD,SAAS,IAGX4H,GAAWA,EAAQgL,aAAejP,GAAOA,EAAIK,OAASL,EAAIK,SA6BtD9D,GAA2CjB,GAC/CwP,EAAUC,MAAM9I,UAAU,CACxBF,KACEvH,IGp/BO,IACb3D,EACA0Y,EACA9T,EAFA5E,EH0/B8ByE,EAAMzE,KGz/BpC0Y,EHy/B0C/U,EAAU3D,KGx/BpD4E,EHw/B0DH,EAAMG,MGt/B/D5E,GACA0Y,GACD1Y,IAAS0Y,IACT5N,EAAsB9K,GAAMwP,KACzBmJ,GACCA,IACC/T,EACG+T,IAAgBD,EAChBC,EAAYjJ,WAAWgJ,IACvBA,EAAWhJ,WAAWiJ,OTPjB,EACbC,EAIAxU,EACAS,EACAf,KAEAe,EAAgB+T,GAChB,MAAM5Y,KAAEA,KAAS2D,GAAciV,EAE/B,OACEpN,EAAc7H,IACdM,OAAOgD,KAAKtD,GAAWf,QAAUqB,OAAOgD,KAAK7C,GAAiBxB,QAC9DqB,OAAOgD,KAAKtD,GAAWyL,KACpB/N,GACC+C,EAAgB/C,OACdyC,GAAUZ,KMm+BV2V,CACElV,EACCc,EAAMd,WAA+BS,EACtC0U,GACArU,EAAMsU,eAGRtU,EAAMmB,SAAS,CACbkC,OAAQ,IAAKC,MACVhD,KACApB,EACHI,cACEC,OAIPsH,YAcCnB,GAA8C,CAACnK,EAAMoN,EAAU,CAAA,KACnE,IAAK,MAAM7G,KAAavG,EAAO8K,EAAsB9K,GAAQiG,EAAO+D,MAClE/D,EAAO+D,MAAMgP,OAAOzS,GACpBN,EAAOoC,MAAM2Q,OAAOzS,GAEf6G,EAAQ6L,YACX9M,GAAM7C,EAAS/C,GACf4F,GAAMpE,EAAaxB,KAGpB6G,EAAQ8L,WAAa/M,GAAMpH,EAAWU,OAAQc,IAC9C6G,EAAQ+L,WAAahN,GAAMpH,EAAWK,YAAamB,IACnD6G,EAAQgM,aAAejN,GAAMpH,EAAWM,cAAekB,IACvD6G,EAAQiM,kBACPlN,GAAMpH,EAAWO,iBAAkBiB,IACpCuD,EAAS3B,mBACPiF,EAAQkM,kBACTnN,GAAMnI,EAAgBuC,GAG1B0N,EAAUC,MAAMhJ,KAAK,CACnBpD,OAAQrH,EAAYsH,KAGtBkM,EAAUC,MAAMhJ,KAAK,IAChBnG,KACEqI,EAAQ+L,UAAiB,CAAEjU,QAASoQ,KAAhB,CAAA,KAG1BlI,EAAQmM,aAAe1T,KAGpBuE,GAAgE,EACpEzF,WACA3E,YAGGwC,EAAUmC,IAAasF,EAAOD,OAC7BrF,GACFsB,EAAOtB,SAAS1E,IAAID,MAEpB2E,EAAWsB,EAAOtB,SAAS0B,IAAIrG,GAAQiG,EAAOtB,SAASqU,OAAOhZ,KAI5DwI,GAA0C,CAACxI,EAAMoN,EAAU,CAAA,KAC/D,IAAI/D,EAAQnH,EAAIoH,EAAStJ,GACzB,MAAMwZ,EACJhX,EAAU4K,EAAQzI,WAAanC,EAAUsH,EAASnF,UAwBpD,OAtBAlC,EAAI6G,EAAStJ,EAAM,IACbqJ,GAAS,CAAA,EACbE,GAAI,IACEF,GAASA,EAAME,GAAKF,EAAME,GAAK,CAAEJ,IAAK,CAAEnJ,SAC5CA,OACAgK,OAAO,KACJoD,KAGPnH,EAAO+D,MAAM3D,IAAIrG,GAEbqJ,EACFe,GAAkB,CAChBzF,SAAUnC,EAAU4K,EAAQzI,UACxByI,EAAQzI,SACRmF,EAASnF,SACb3E,SAGF4U,EAAoB5U,GAAM,EAAMoN,EAAQjO,OAGnC,IACDqa,EACA,CAAE7U,SAAUyI,EAAQzI,UAAYmF,EAASnF,UACzC,MACAmF,EAAS2P,YACT,CACEzI,WAAY5D,EAAQ4D,SACpBG,IAAK7C,GAAalB,EAAQ+D,KAC1BC,IAAK9C,GAAalB,EAAQgE,KAC1BF,UAAW5C,GAAqBlB,EAAQ8D,WACxCD,UAAW3C,GAAalB,EAAQ6D,WAChCI,QAAS/C,GAAalB,EAAQiE,UAEhC,GACJrR,OACAgJ,WACAE,OAAQF,EACRG,IAAMA,IACJ,GAAIA,EAAK,CACPX,GAASxI,EAAMoN,GACf/D,EAAQnH,EAAIoH,EAAStJ,GAErB,MAAM0Z,EAAWlY,EAAY2H,EAAIhK,QAC7BgK,EAAIwQ,kBACDxQ,EAAIwQ,iBAAiB,yBAAyB,IAEjDxQ,EACEyQ,EI1nCD,CAACzQ,GACd6C,GAAa7C,IAAQpK,EAAgBoK,GJynCLsI,CAAkBiI,GACpCzL,EAAO5E,EAAME,GAAG0E,MAAQ,GAE9B,GACE2L,EACI3L,EAAKmB,KAAM/B,GAAgBA,IAAWqM,GACtCA,IAAarQ,EAAME,GAAGJ,IAE1B,OAGF1G,EAAI6G,EAAStJ,EAAM,CACjBuJ,GAAI,IACCF,EAAME,MACLqQ,EACA,CACE3L,KAAM,IACDA,EAAKrM,OAAOqK,IACfyN,KACIla,MAAMC,QAAQyC,EAAI8B,EAAgBhE,IAAS,CAAC,IAAM,IAExDmJ,IAAK,CAAElK,KAAMya,EAASza,KAAMe,SAE9B,CAAEmJ,IAAKuQ,MAIf9E,EAAoB5U,GAAM,OAAO0B,EAAWgY,QAE5CrQ,EAAQnH,EAAIoH,EAAStJ,EAAM,CAAA,GAEvBqJ,EAAME,KACRF,EAAME,GAAGS,OAAQ,IAGlBF,EAAS3B,kBAAoBiF,EAAQjF,qBAClCrI,EAAmBmG,EAAOoC,MAAOrI,KAASiK,EAAOC,SACnDjE,EAAO6N,QAAQzN,IAAIrG,MAMvB6Z,GAAc,IAClB/P,EAASuJ,kBACT1D,GAAsBrG,EAASyO,EAAa9R,EAAO+D,OAyB/C8P,GACJ,CAACC,EAASC,IAAcpJ,MAAOqJ,IAC7B,IAAIC,EACAD,IACFA,EAAEE,gBAAkBF,EAAEE,iBACrBF,EAA+BG,SAC7BH,EAA+BG,WAEpC,IAAIC,EACF5Z,EAAYsH,GAMd,GAJAkM,EAAUC,MAAMhJ,KAAK,CACnByI,cAAc,IAGZ7J,EAASwK,SAAU,CACrB,MAAM7O,OAAEA,EAAMqC,OAAEA,SAAiByM,IACjCxP,EAAWU,OAASA,EACpB4U,EAAc5Z,EAAYqH,cAEpB0M,EAAyBlL,GAGjC,GAAIrD,EAAOtB,SAAS2V,KAClB,IAAK,MAAMta,KAAQiG,EAAOtB,SACxBwH,GAAMkO,EAAara,GAMvB,GAFAmM,GAAMpH,EAAWU,OAAQ,QAErB+F,EAAczG,EAAWU,QAAS,CACpCwO,EAAUC,MAAMhJ,KAAK,CACnBzF,OAAQ,CAAA,IAEV,UACQsU,EAAQM,EAAmCJ,GACjD,MAAOlR,GACPmR,EAAenR,QAGbiR,SACIA,EAAU,IAAKjV,EAAWU,QAAUwU,GAE5CJ,KACA9D,WAAW8D,IAUb,GAPA5F,EAAUC,MAAMhJ,KAAK,CACnBwI,aAAa,EACbC,cAAc,EACdC,mBAAoBpI,EAAczG,EAAWU,UAAYyU,EACzD1G,YAAazO,EAAWyO,YAAc,EACtC/N,OAAQV,EAAWU,SAEjByU,EACF,MAAMA,GAoCNK,GAAqC,CACzCrU,EACAsU,EAAmB,CAAA,KAEnB,MAAMC,EAAgBvU,EAAazF,EAAYyF,GAAclC,EACvD0W,EAAqBja,EAAYga,GACjCE,EAAqBnP,EAActF,GACnC4B,EAAS6S,EAAqB3W,EAAiB0W,EAMrD,GAJKF,EAAiBI,oBACpB5W,EAAiByW,IAGdD,EAAiBK,WAAY,CAChC,GAAIL,EAAiBM,gBAAiB,CACpC,MAAMC,EAAgB,IAAIlH,IAAI,IACzB5N,EAAO+D,SACP/F,OAAOgD,KAAK+F,GAAehJ,EAAgB+D,MAEhD,IAAK,MAAMxB,KAAa/G,MAAMkV,KAAKqG,GACjC7Y,EAAI6C,EAAWK,YAAamB,GACxB9D,EAAIqF,EAAQvB,EAAWrE,EAAI6F,EAAaxB,IACxC2Q,EACE3Q,EACArE,EAAI4F,EAAQvB,QAGf,CACL,GAAIlG,GAASmB,EAAY0E,GACvB,IAAK,MAAMlG,KAAQiG,EAAO+D,MAAO,CAC/B,MAAMX,EAAQnH,EAAIoH,EAAStJ,GAC3B,GAAIqJ,GAASA,EAAME,GAAI,CACrB,MAAM2F,EAAiB1P,MAAMC,QAAQ4J,EAAME,GAAG0E,MAC1C5E,EAAME,GAAG0E,KAAK,GACd5E,EAAME,GAAGJ,IAEb,GAAIwC,EAAcuD,GAAiB,CACjC,MAAM8L,EAAO9L,EAAe+L,QAAQ,QACpC,GAAID,EAAM,CACRA,EAAKE,QACL,SAOV,GAAIV,EAAiBW,cACnB,IAAK,MAAM5U,KAAaN,EAAO+D,MAC7BkN,EACE3Q,EACArE,EAAI4F,EAAQvB,SAIhB+C,EAAU,CAAA,EAIdvB,EAAc+B,EAAS3B,iBACnBqS,EAAiBI,kBACdna,EAAYuD,GACZ,CAAA,EACFvD,EAAYqH,GAEjBmM,EAAU5L,MAAM6C,KAAK,CACnBpD,OAAQ,IAAKA,KAGfmM,EAAUC,MAAMhJ,KAAK,CACnBpD,OAAQ,IAAKA,KAIjB7B,EAAS,CACP+D,MAAOwQ,EAAiBM,gBAAkB7U,EAAO+D,MAAQ,IAAI6J,IAC7DC,QAAS,IAAID,IACbxL,MAAO,IAAIwL,IACXlP,SAAU,IAAIkP,IACdzN,MAAO,IAAIyN,IACXrN,UAAU,EACVgD,MAAO,IAGTS,EAAOD,OACJ5F,EAAgBoB,WACfgV,EAAiBjB,eACjBiB,EAAiBM,gBAErB7Q,EAAO7D,QAAU0D,EAAS3B,iBAE1B8L,EAAUC,MAAMhJ,KAAK,CACnBsI,YAAagH,EAAiBY,gBAC1BrW,EAAWyO,YACX,EACJtO,SAASyV,IAELH,EAAiBrB,UACfpU,EAAWG,WAETsV,EAAiBI,mBAChBlU,EAAUR,EAAYlC,KAE/B0P,cAAa8G,EAAiBa,iBAC1BtW,EAAW2O,YAEftO,YAAauV,EACT,CAAA,EACAH,EAAiBM,gBACfN,EAAiBI,mBAAqB7S,EACpCiF,GAAehJ,EAAgB+D,GAC/BhD,EAAWK,YACboV,EAAiBI,mBAAqB1U,EACpC8G,GAAehJ,EAAgBkC,GAC/BsU,EAAiBrB,UACfpU,EAAWK,YACX,CAAA,EACVC,cAAemV,EAAiBpB,YAC5BrU,EAAWM,cACX,CAAA,EACJI,OAAQ+U,EAAiBc,WAAavW,EAAWU,OAAS,CAAA,EAC1DmO,qBAAoB4G,EAAiBe,wBACjCxW,EAAW6O,mBAEfD,cAAc,KAIZuH,GAAoC,CAAChV,EAAYsU,IACrDD,GACE7O,EAAWxF,GACNA,EAAwB6B,GACzB7B,EACJsU,GAqBE1B,GACJ9C,IAEAjR,EAAa,IACRA,KACAiR,IAaDtR,GAAU,CACdd,QAAS,CACP4E,YACA2B,cACAkO,iBACAyB,gBACAxB,YACA5S,cACA6O,aACAsF,eACAjS,YACA0N,YACAzP,YACA2V,eA5vC0C,CAC5Cxb,EACA8H,EAAS,GACT2T,EACAC,EACAC,GAAkB,EAClBC,GAA6B,KAE7B,GAAIF,GAAQD,IAAW3R,EAASnF,SAAU,CAExC,GADAsF,EAAOC,QAAS,EACZ0R,GAA8Bpc,MAAMC,QAAQyC,EAAIoH,EAAStJ,IAAQ,CACnE,MAAMqa,EAAcoB,EAAOvZ,EAAIoH,EAAStJ,GAAO0b,EAAKG,KAAMH,EAAKI,MAC/DH,GAAmBlZ,EAAI6G,EAAStJ,EAAMqa,GAGxC,GACEuB,GACApc,MAAMC,QAAQyC,EAAI6C,EAAWU,OAAQzF,IACrC,CACA,MAAMyF,EAASgW,EACbvZ,EAAI6C,EAAWU,OAAQzF,GACvB0b,EAAKG,KACLH,EAAKI,MAEPH,GAAmBlZ,EAAIsC,EAAWU,OAAQzF,EAAMyF,GKlPzC,EAAI0D,EAAQnJ,MACxB2B,EAAQO,EAAIiH,EAAKnJ,IAAO4C,QAAUuJ,GAAMhD,EAAKnJ,ILkPxC+b,CAAgBhX,EAAWU,OAAQzF,GAGrC,IACGoE,EAAgBiB,eACf2O,EAAyB3O,gBAC3BuW,GACApc,MAAMC,QAAQyC,EAAI6C,EAAWM,cAAerF,IAC5C,CACA,MAAMqF,EAAgBoW,EACpBvZ,EAAI6C,EAAWM,cAAerF,GAC9B0b,EAAKG,KACLH,EAAKI,MAEPH,GAAmBlZ,EAAIsC,EAAWM,cAAerF,EAAMqF,IAGrDjB,EAAgBgB,aAAe4O,EAAyB5O,eAC1DL,EAAWK,YAAc4H,GAAehJ,EAAgB+D,IAG1DkM,EAAUC,MAAMhJ,KAAK,CACnBlL,OACAkF,QAASoQ,EAAUtV,EAAM8H,GACzB1C,YAAaL,EAAWK,YACxBK,OAAQV,EAAWU,OACnBD,QAAST,EAAWS,eAGtB/C,EAAIsF,EAAa/H,EAAM8H,IAusCvBsC,qBACA4R,WA7rCgBvW,IAClBV,EAAWU,OAASA,EACpBwO,EAAUC,MAAMhJ,KAAK,CACnBzF,OAAQV,EAAWU,OACnBD,SAAS,KA0rCTyW,eA/5BFjc,GAEA2B,EACEO,EACE+H,EAAOD,MAAQjC,EAAc/D,EAC7BhE,EACA8J,EAAS3B,iBAAmBjG,EAAI8B,EAAgBhE,EAAM,IAAM,KA05B9Dua,UACA2B,oBA3BwB,IAC1BxQ,EAAW5B,EAAS/F,gBACnB+F,EAAS/F,gBAA6BoY,KAAMrU,IAC3CoT,GAAMpT,EAAQgC,EAASsS,cACvBnI,EAAUC,MAAMhJ,KAAK,CACnB/F,WAAW,MAuBb8C,iBA38BqB,KACvB,IAAK,MAAMjI,KAAQiG,EAAO6N,QAAS,CACjC,MAAMzK,EAAenH,EAAIoH,EAAStJ,GAElCqJ,IACGA,EAAME,GAAG0E,KACN5E,EAAME,GAAG0E,KAAKuC,MAAOrH,IAAS8C,GAAK9C,KAClC8C,GAAK5C,EAAME,GAAGJ,OACnBgB,GAAWnK,GAGfiG,EAAO6N,QAAU,IAAID,KAi8BnBwI,aAnTkB1X,IAChBnC,EAAUmC,KACZsP,EAAUC,MAAMhJ,KAAK,CAAEvG,aACvBgL,GACErG,EACA,CAACH,EAAKnJ,KACJ,MAAM8P,EAAsB5N,EAAIoH,EAAStJ,GACrC8P,IACF3G,EAAIxE,SAAWmL,EAAavG,GAAG5E,UAAYA,EAEvCnF,MAAMC,QAAQqQ,EAAavG,GAAG0E,OAChC6B,EAAavG,GAAG0E,KAAK0G,QAASpD,IAC5BA,EAAS5M,SAAWmL,EAAavG,GAAG5E,UAAYA,MAKxD,GACA,KAkSFsP,YACA7P,kBACA,WAAIkF,GACF,OAAOA,GAET,eAAIvB,GACF,OAAOA,GAET,UAAIkC,GACF,OAAOA,GAET,UAAIA,CAAO9K,GACT8K,EAAS9K,GAEX,kBAAI6E,GACF,OAAOA,GAET,UAAIiC,GACF,OAAOA,GAET,UAAIA,CAAO9G,GACT8G,EAAS9G,GAEX,cAAI4F,GACF,OAAOA,GAET,YAAI+E,GACF,OAAOA,GAET,YAAIA,CAAS3K,GACX2K,EAAW,IACNA,KACA3K,KAITiM,UAtfiD3G,IACjDwF,EAAOD,OAAQ,EACfgK,EAA2B,IACtBA,KACAvP,EAAMd,WAEJ+B,GAAW,IACbjB,EACHd,UAAWqQ,KA+eb+C,WACAvO,YACAsR,gBACA1T,MAjjBwC,CACxCpG,EAIAqC,IAEAqJ,EAAW1L,GACPiU,EAAUC,MAAM9I,UAAU,CACxBF,KAAOoR,GACL,WAAYA,GACZtc,EACE4H,OAAUlG,EAAWW,GACrBia,KAON1U,EACE5H,EACAqC,GACA,GA2hBN6U,WACAV,aACA0E,SACAqB,WA7QkD,CAACvc,EAAMoN,EAAU,CAAA,KAC/DlL,EAAIoH,EAAStJ,KACXwB,EAAY4L,EAAQ/K,cACtB6U,EAASlX,EAAMS,EAAYyB,EAAI8B,EAAgBhE,MAE/CkX,EACElX,EACAoN,EAAQ/K,cAEVI,EAAIuB,EAAgBhE,EAAMS,EAAY2M,EAAQ/K,gBAG3C+K,EAAQgM,aACXjN,GAAMpH,EAAWM,cAAerF,GAG7BoN,EAAQ+L,YACXhN,GAAMpH,EAAWK,YAAapF,GAC9B+E,EAAWG,QAAUkI,EAAQ/K,aACzBiT,EAAUtV,EAAMS,EAAYyB,EAAI8B,EAAgBhE,KAChDsV,KAGDlI,EAAQ8L,YACX/M,GAAMpH,EAAWU,OAAQzF,GACzBoE,EAAgBoB,SAAWK,KAG7BoO,EAAUC,MAAMhJ,KAAK,IAAKnG,MAkP5ByX,YAvlBqDxc,IACrDA,GACE8K,EAAsB9K,GAAM2U,QAAS8H,GACnCtQ,GAAMpH,EAAWU,OAAQgX,IAG7BxI,EAAUC,MAAMhJ,KAAK,CACnBzF,OAAQzF,EAAO+E,EAAWU,OAAS,CAAA,KAilBrC0E,cACAmO,YACAoE,SAzG8C,CAAC1c,EAAMoN,EAAU,CAAA,KAC/D,MAAM/D,EAAQnH,EAAIoH,EAAStJ,GACrBkP,EAAiB7F,GAASA,EAAME,GAEtC,GAAI2F,EAAgB,CAClB,MAAMwK,EAAWxK,EAAejB,KAC5BiB,EAAejB,KAAK,GACpBiB,EAAe/F,IAEfuQ,EAASlQ,QACXkQ,EAASlQ,QACT4D,EAAQuP,cACNjR,EAAWgO,EAASjQ,SACpBiQ,EAASjQ,YA6Ff4O,kBAGF,MAAO,IACF3T,GACHkY,YAAalY,GAEjB,CM1hDA,IAAAmY,GAAe,KACb,GAAsB,oBAAXC,QAA0BA,OAAOC,WAC1C,OAAOD,OAAOC,aAGhB,MAAMC,EACmB,oBAAhBC,YAA8B7d,KAAK8d,MAA4B,IAApBD,YAAYC,MAEhE,MAAO,uCAAuClb,QAAQ,QAAUmb,IAC9D,MAAMC,GAAqB,GAAhBC,KAAKC,SAAgBN,GAAK,GAAK,EAE1C,OAAa,KAALG,EAAWC,EAAS,EAAJA,EAAW,GAAKG,SAAS,OCRrDC,GAAe,CACbxd,EACA0C,EACA0K,EAAiC,CAAA,IAEjCA,EAAQgL,aAAe5W,EAAY4L,EAAQgL,aACvChL,EAAQqQ,WACR,GAAGzd,KAAQwB,EAAY4L,EAAQsQ,YAAchb,EAAQ0K,EAAQsQ,cAC7D,GCTNC,GAAe,CAAIjd,EAAWvB,IAAwB,IACjDuB,KACAoK,EAAsB3L,ICJ3Bye,GAAmBze,GACjBK,MAAMC,QAAQN,GAASA,EAAMmH,IAAI,aAAmB5E,ECOxC,SAAUmc,GACtBnd,EACAgC,EACAvD,GAEA,MAAO,IACFuB,EAAK6L,MAAM,EAAG7J,MACdoI,EAAsB3L,MACtBuB,EAAK6L,MAAM7J,GAElB,CChBA,IAAAob,GAAe,CACbpd,EACAgU,EACAqJ,IAEKve,MAAMC,QAAQiB,IAIfc,EAAYd,EAAKqd,MACnBrd,EAAKqd,QAAMrc,GAEbhB,EAAKsd,OAAOD,EAAI,EAAGrd,EAAKsd,OAAOtJ,EAAM,GAAG,IAEjChU,GARE,GCNXud,GAAe,CAAIvd,EAAWvB,IAAwB,IACjD2L,EAAsB3L,MACtB2L,EAAsBpK,ICY3B,IAAAwd,GAAe,CAAIxd,EAAWgC,IAC5BlB,EAAYkB,GACR,GAdN,SAA4BhC,EAAWyd,GACrC,IAAIC,EAAI,EACR,MAAMC,EAAO,IAAI3d,GAEjB,IAAK,MAAMgC,KAASyb,EAClBE,EAAKL,OAAOtb,EAAQ0b,EAAG,GACvBA,IAGF,OAAOzc,EAAQ0c,GAAMzb,OAASyb,EAAO,EACvC,CAKMC,CACE5d,EACCoK,EAAsBpI,GAAoB6b,KAAK,CAACC,EAAGC,IAAMD,EAAIC,ICrBtEC,GAAe,CAAIhe,EAAWie,EAAgBC,MAC3Cle,EAAKie,GAASje,EAAKke,IAAW,CAACle,EAAKke,GAASle,EAAKie,KCDrDE,GAAe,CAAIxE,EAAkB3X,EAAevD,KAClDkb,EAAY3X,GAASvD,EACdkb,gBjDgDP5V,GAEAA,EAAMqa,OAAO5W,EAAuDzD,WEtBtE,SAGEA,GACA,MAAMC,EAAUlB,KACTub,EAASC,GAAc3b,EAAMyB,UAAS,IACvClB,QACJA,EAAUc,EAAQd,QAAOqb,SACzBA,EAAQC,SACRA,EAAQhV,OACRA,EAAMuR,OACNA,EAAS/Q,EAAYyU,QACrBA,EAAOC,QACPA,EAAOC,QACPA,EAAOP,OACPA,EAAMQ,UACNA,EAASC,eACTA,KACGC,GACD/a,EAEEgb,EAAS7O,MAAOjR,IACpB,IAAI+f,GAAW,EACXzgB,EAAO,SAEL2E,EAAQkW,aAAalJ,MAAOlQ,IAChC,MAAMif,EAAW,IAAIC,SACrB,IAAIC,EAAe,GAEnB,IACEA,EAAeC,KAAKC,UAAUrf,GAC9B,MAAAsf,GAAM,CAER,MAAMC,EAAoB5V,EAAQzG,EAAQmE,aAE1C,IAAK,MAAM1G,KAAO4e,EAChBN,EAASO,OAAO7e,EAAK4e,EAAkB5e,IAazC,GAVI4d,SACIA,EAAS,CACbve,OACAf,QACA8b,SACAkE,WACAE,iBAIA3V,EACF,IACE,MAAMiW,EAAgC,CACpChB,GAAWA,EAAQ,gBACnBC,GACA5P,KAAMrQ,GAAUA,GAASA,EAAMiI,SAAS,SAEpCgZ,QAAiBC,MAAMC,OAAOpW,GAAS,CAC3CuR,SACA0D,QAAS,IACJA,KACCC,GAAuB,wBAAZA,EACX,CAAE,eAAgBA,GAClB,IAENmB,KAAMJ,EAAgCN,EAAeF,IAIrDS,IACCb,GACIA,EAAea,EAASI,QACzBJ,EAASI,OAAS,KAAOJ,EAASI,QAAU,MAEhDd,GAAW,EACXL,GAAWA,EAAQ,CAAEe,aACrBnhB,EAAOqhB,OAAOF,EAASI,SAEvBlB,GAAaA,EAAU,CAAEc,aAE3B,MAAOrX,GACP2W,GAAW,EACXL,GAAWA,EAAQ,CAAEtW,YAxDrBnF,CA2DHjE,GAEC+f,GAAYjb,EAAMb,UACpBa,EAAMb,QAAQqQ,UAAUC,MAAMhJ,KAAK,CACjC0I,oBAAoB,IAEtBnP,EAAMb,QAAQ0U,SAAS,cAAe,CACpCrZ,WASN,OAJAoE,EAAMkB,UAAU,KACdya,GAAW,IACV,IAEIF,EACLzb,EAAAod,cAAApd,EAAAqd,SAAA,KACG5B,EAAO,CACNW,YAIJpc,EAAAod,cAAA,OAAA,CACEE,WAAY5B,EACZ7U,OAAQA,EACRuR,OAAQA,EACR2D,QAASA,EACTH,SAAUQ,KACND,GAEHN,EAGP,iBZjEEza,IAEA,MAAMya,SAAEA,KAAaxe,GAAS+D,EAC9B,OACEpB,EAAAod,cAACrd,EAAgBwd,SAAQ,CAACzhB,MAAOuB,GAC9Bwe,8F4DRD,SAOJza,GAOA,MAAMC,EAAUlB,KACVI,QACJA,EAAUc,EAAQd,QAAO5D,KACzBA,EAAI6gB,QACJA,EAAU,KAAI1Y,iBACdA,EAAgBM,MAChBA,GACEhE,GACGmI,EAAQkU,GAAazd,EAAMyB,SAASlB,EAAQqY,eAAejc,IAC5D+gB,EAAM1d,EAAM4B,OAChBrB,EAAQqY,eAAejc,GAAMsG,IAAIuW,KAE7BmE,EAAY3d,EAAM4B,OAAO2H,GACzBqU,EAAY5d,EAAM4B,QAAO,GAE/B+b,EAAUrb,QAAUiH,EACpBhJ,EAAQqC,OAAOoC,MAAMhC,IAAIrG,GAEzBqD,EAAMyC,QACJ,IACE2C,GACC7E,EAA2D4E,SAC1DxI,EACAyI,GAEJ,CAAC7E,EAAS6E,EAAOzI,IAGnBqE,EACE,IACET,EAAQqQ,UAAU5L,MAAM+C,UAAU,CAChCF,KAAM,EACJpD,SACA9H,KAAMkhB,MAKN,GAAIA,IAAmBlhB,IAASkhB,EAAgB,CAC9C,MAAM7G,EAAcnY,EAAI4F,EAAQ9H,GAC5BR,MAAMC,QAAQ4a,KAChByG,EAAUzG,GACV0G,EAAIpb,QAAU0U,EAAY/T,IAAIuW,SAInCvR,YACL,CAAC1H,EAAS5D,IAGZ,MAAMmhB,EAAe9d,EAAM4F,YAMvBmY,IAEAH,EAAUtb,SAAU,EACpB/B,EAAQ4X,eAAexb,EAAMohB,IAE/B,CAACxd,EAAS5D,IAqRZ,OA5GAqD,EAAMkB,UAAU,KAQd,GAPAX,EAAQqG,OAAOC,QAAS,EAExBoF,GAAUtP,EAAM4D,EAAQqC,SACtBrC,EAAQqQ,UAAUC,MAAMhJ,KAAK,IACxBtH,EAAQmB,aAIbkc,EAAUtb,WACR8I,GAAmB7K,EAAQkG,SAAS4E,MAAMC,YAC1C/K,EAAQmB,WAAW2O,eACpBjF,GAAmB7K,EAAQkG,SAASsJ,gBAAgBzE,WAErD,GAAI/K,EAAQkG,SAASwK,SACnB1Q,EAAQ2Q,WAAW,CAACvU,IAAOmc,KAAM7Z,IAC/B,MAAMyG,EAAQ7G,EAAII,EAAOmD,OAAQzF,GAC3BqhB,EAAgBnf,EAAI0B,EAAQmB,WAAWU,OAAQzF,IAGnDqhB,GACMtY,GAASsY,EAAcpiB,MACxB8J,IACEsY,EAAcpiB,OAAS8J,EAAM9J,MAC5BoiB,EAAc1X,UAAYZ,EAAMY,SACpCZ,GAASA,EAAM9J,QAEnB8J,EACItG,EAAImB,EAAQmB,WAAWU,OAAQzF,EAAM+I,GACrCoD,GAAMvI,EAAQmB,WAAWU,OAAQzF,GACrC4D,EAAQqQ,UAAUC,MAAMhJ,KAAK,CAC3BzF,OAAQ7B,EAAQmB,WAAWU,gBAI5B,CACL,MAAM4D,EAAenH,EAAI0B,EAAQ0F,QAAStJ,IAExCqJ,IACAA,EAAME,IAEJkF,GAAmB7K,EAAQkG,SAASsJ,gBAAgBzE,YACpDF,GAAmB7K,EAAQkG,SAAS4E,MAAMC,YAG5CgC,GACEtH,EACAzF,EAAQqC,OAAOtB,SACff,EAAQmE,YACRnE,EAAQkG,SAASsK,eAAiBlR,EAClCU,EAAQkG,SAASgH,2BACjB,GACAqL,KACCpT,IACEyC,EAAczC,IACfnF,EAAQqQ,UAAUC,MAAMhJ,KAAK,CAC3BzF,OAAQ2K,GACNxM,EAAQmB,WAAWU,OACnBsD,EACA/I,MAQd4D,EAAQqQ,UAAUC,MAAMhJ,KAAK,CAC3BlL,OACA8H,OAAQrH,EAAYmD,EAAQmE,eAG9BnE,EAAQqC,OAAOuD,OACbmG,GAAsB/L,EAAQ0F,QAAS,CAACH,EAAK9H,KAC3C,GACEuC,EAAQqC,OAAOuD,OACfnI,EAAIqO,WAAW9L,EAAQqC,OAAOuD,QAC9BL,EAAIK,MAGJ,OADAL,EAAIK,QACG,IAKb5F,EAAQqC,OAAOuD,MAAQ,GAEvB5F,EAAQiC,YACRob,EAAUtb,SAAU,GACnB,CAACiH,EAAQ5M,EAAM4D,IAElBP,EAAMkB,UAAU,MACbrC,EAAI0B,EAAQmE,YAAa/H,IAAS4D,EAAQ4X,eAAexb,GAEnD,KAQL4D,EAAQkG,SAAS3B,kBAAoBA,EACjCvE,EAAQuG,WAAWnK,GARD,EAACA,EAAyBb,KAC9C,MAAMkK,EAAenH,EAAI0B,EAAQ0F,QAAStJ,GACtCqJ,GAASA,EAAME,KACjBF,EAAME,GAAGS,MAAQ7K,IAMjB4K,CAAc/J,GAAM,KAEzB,CAACA,EAAM4D,EAASid,EAAS1Y,IAErB,CACLmZ,KAAMje,EAAM4F,YAlMD,CAAC0V,EAAgBC,KAC5B,MAAMwC,EAA0Bxd,EAAQqY,eAAejc,GACvD0e,GAAY0C,EAAyBzC,EAAQC,GAC7CF,GAAYqC,EAAIpb,QAASgZ,EAAQC,GACjCuC,EAAaC,GACbN,EAAUM,GACVxd,EAAQ4X,eACNxb,EACAohB,EACA1C,GACA,CACE7C,KAAM8C,EACN7C,KAAM8C,IAER,IAoL4B,CAACuC,EAAcnhB,EAAM4D,IACnD2d,KAAMle,EAAM4F,YAjLD,CAACyL,EAAcqJ,KAC1B,MAAMqD,EAA0Bxd,EAAQqY,eAAejc,GACvD8d,GAAYsD,EAAyB1M,EAAMqJ,GAC3CD,GAAYiD,EAAIpb,QAAS+O,EAAMqJ,GAC/BoD,EAAaC,GACbN,EAAUM,GACVxd,EAAQ4X,eACNxb,EACAohB,EACAtD,GACA,CACEjC,KAAMnH,EACNoH,KAAMiC,IAER,IAmK4B,CAACoD,EAAcnhB,EAAM4D,IACnD4d,QAASne,EAAM4F,YA7PD,CACd9J,EAGAiO,KAEA,MAAMqU,EAAe3W,EAAsBrK,EAAYtB,IACjDiiB,EAA0BnD,GAC9Bra,EAAQqY,eAAejc,GACvByhB,GAEF7d,EAAQqC,OAAOuD,MAAQgU,GAAkBxd,EAAM,EAAGoN,GAClD2T,EAAIpb,QAAUsY,GAAU8C,EAAIpb,QAAS8b,EAAanb,IAAIuW,KACtDsE,EAAaC,GACbN,EAAUM,GACVxd,EAAQ4X,eAAexb,EAAMohB,EAAyBnD,GAAW,CAC/DpC,KAAM+B,GAAeze,MA6Oa,CAACgiB,EAAcnhB,EAAM4D,IACzDsc,OAAQ7c,EAAM4F,YAtRD,CACb9J,EAGAiO,KAEA,MAAMsU,EAAc5W,EAAsBrK,EAAYtB,IAChDiiB,EAA0BzD,GAC9B/Z,EAAQqY,eAAejc,GACvB0hB,GAEF9d,EAAQqC,OAAOuD,MAAQgU,GACrBxd,EACAohB,EAAwBxe,OAAS,EACjCwK,GAEF2T,EAAIpb,QAAUgY,GAASoD,EAAIpb,QAAS+b,EAAYpb,IAAIuW,KACpDsE,EAAaC,GACbN,EAAUM,GACVxd,EAAQ4X,eAAexb,EAAMohB,EAAyBzD,GAAU,CAC9D9B,KAAM+B,GAAeze,MAkQW,CAACgiB,EAAcnhB,EAAM4D,IACvD+d,OAAQte,EAAM4F,YA3OAvG,IACd,MAAM0e,EAEAlD,GAActa,EAAQqY,eAAejc,GAAO0C,GAClDqe,EAAIpb,QAAUuY,GAAc6C,EAAIpb,QAASjD,GACzCye,EAAaC,GACbN,EAAUM,IACT5hB,MAAMC,QAAQyC,EAAI0B,EAAQ0F,QAAStJ,KAClCyC,EAAImB,EAAQ0F,QAAStJ,OAAM0B,GAC7BkC,EAAQ4X,eAAexb,EAAMohB,EAAyBlD,GAAe,CACnErC,KAAMnZ,KAiO0B,CAACye,EAAcnhB,EAAM4D,IACvDia,OAAQxa,EAAM4F,YA9ND,CACbvG,EACAvD,EAGAiO,KAEA,MAAMwU,EAAc9W,EAAsBrK,EAAYtB,IAChDiiB,EAA0BS,GAC9Bje,EAAQqY,eAAejc,GACvB0C,EACAkf,GAEFhe,EAAQqC,OAAOuD,MAAQgU,GAAkBxd,EAAM0C,EAAO0K,GACtD2T,EAAIpb,QAAUkc,GAASd,EAAIpb,QAASjD,EAAOkf,EAAYtb,IAAIuW,KAC3DsE,EAAaC,GACbN,EAAUM,GACVxd,EAAQ4X,eAAexb,EAAMohB,EAAyBS,GAAU,CAC9DhG,KAAMnZ,EACNoZ,KAAM8B,GAAeze,MA2MW,CAACgiB,EAAcnhB,EAAM4D,IACvDke,OAAQze,EAAM4F,YApKD,CACbvG,EACAvD,KAEA,MAAM0I,EAAcpH,EAAYtB,GAC1BiiB,EAA0BvC,GAC9Bjb,EAAQqY,eAENjc,GACF0C,EACAmF,GAEFkZ,EAAIpb,QAAU,IAAIyb,GAAyB9a,IAAI,CAACyb,EAAM3D,IACnD2D,GAAQ3D,IAAM1b,EAAuBqe,EAAIpb,QAAQyY,GAA3BvB,MAEzBsE,EAAaC,GACbN,EAAU,IAAIM,IACdxd,EAAQ4X,eACNxb,EACAohB,EACAvC,GACA,CACEhD,KAAMnZ,EACNoZ,KAAMjU,IAER,GACA,IA0IgC,CAACsZ,EAAcnhB,EAAM4D,IACvD5B,QAASqB,EAAM4F,YAtIf9J,IAIA,MAAMiiB,EAA0BtW,EAAsBrK,EAAYtB,IAClE4hB,EAAIpb,QAAUyb,EAAwB9a,IAAIuW,IAC1CsE,EAAa,IAAIC,IACjBN,EAAU,IAAIM,IACdxd,EAAQ4X,eACNxb,EACA,IAAIohB,GACA1gB,GAAeA,EACnB,IACA,GACA,IAwHkC,CAACygB,EAAcnhB,EAAM4D,IACzDgJ,OAAQvJ,EAAMyC,QACZ,IACE8G,EAAOtG,IAAI,CAAC+C,EAAO3G,KAAK,IACnB2G,EACHwX,CAACA,GAAUE,EAAIpb,QAAQjD,IAAUma,QAErC,CAACjQ,EAAQiU,IAGf,YCvZM,SAKJpc,EAAkE,IAElE,MAAMud,EAAe3e,EAAM4B,YAEzBvD,GACIugB,EAAU5e,EAAM4B,YAA4BvD,IAC3CiC,EAAWkB,GAAmBxB,EAAMyB,SAAkC,CAC3EI,SAAS,EACTK,cAAc,EACdJ,UAAWuG,EAAWjH,EAAMV,eAC5B2P,aAAa,EACbC,cAAc,EACdC,oBAAoB,EACpBpO,SAAS,EACTgO,YAAa,EACbpO,YAAa,CAAA,EACbC,cAAe,CAAA,EACfC,iBAAkB,CAAA,EAClBG,OAAQhB,EAAMgB,QAAU,CAAA,EACxBd,SAAUF,EAAME,WAAY,EAC5B8O,SAAS,EACT1P,cAAe2H,EAAWjH,EAAMV,oBAC5BrC,EACA+C,EAAMV,gBAGZ,IAAKie,EAAarc,QAChB,GAAIlB,EAAMmY,YACRoF,EAAarc,QAAU,IAClBlB,EAAMmY,YACTjZ,aAGEc,EAAMV,gBAAkB2H,EAAWjH,EAAMV,gBAC3CU,EAAMmY,YAAY1B,MAAMzW,EAAMV,cAAeU,EAAM2X,kBAEhD,CACL,MAAMQ,YAAEA,KAAgB4C,GAASlM,GAAkB7O,GAEnDud,EAAarc,QAAU,IAClB6Z,EACH7b,aAKN,MAAMC,EAAUoe,EAAarc,QAAQ/B,QAwFrC,OAvFAA,EAAQkG,SAAWrF,EAEnBJ,EAA0B,KACxB,MAAM6d,EAAMte,EAAQ8B,WAAW,CAC7B/B,UAAWC,EAAQQ,gBACnBwB,SAAU,IAAMf,EAAgB,IAAKjB,EAAQmB,aAC7CgU,cAAc,IAUhB,OAPAlU,EAAiBnE,IAAI,IAChBA,EACH+S,SAAS,KAGX7P,EAAQmB,WAAW0O,SAAU,EAEtByO,GACN,CAACte,IAEJP,EAAMkB,UACJ,IAAMX,EAAQyY,aAAa5X,EAAME,UACjC,CAACf,EAASa,EAAME,WAGlBtB,EAAMkB,UAAU,KACVE,EAAMiK,OACR9K,EAAQkG,SAAS4E,KAAOjK,EAAMiK,MAE5BjK,EAAM2O,iBACRxP,EAAQkG,SAASsJ,eAAiB3O,EAAM2O,iBAEzC,CAACxP,EAASa,EAAMiK,KAAMjK,EAAM2O,iBAE/B/P,EAAMkB,UAAU,KACVE,EAAMgB,SACR7B,EAAQoY,WAAWvX,EAAMgB,QACzB7B,EAAQiW,gBAET,CAACjW,EAASa,EAAMgB,SAEnBpC,EAAMkB,UAAU,KACdE,EAAM0D,kBACJvE,EAAQqQ,UAAUC,MAAMhJ,KAAK,CAC3BpD,OAAQlE,EAAQgE,eAEnB,CAAChE,EAASa,EAAM0D,mBAEnB9E,EAAMkB,UAAU,KACd,GAAIX,EAAQQ,gBAAgBc,QAAS,CACnC,MAAMA,EAAUtB,EAAQ0R,YACpBpQ,IAAYvB,EAAUuB,SACxBtB,EAAQqQ,UAAUC,MAAMhJ,KAAK,CAC3BhG,cAIL,CAACtB,EAASD,EAAUuB,UAEvB7B,EAAMkB,UAAU,KACVE,EAAMqD,SAAWpB,EAAUjC,EAAMqD,OAAQma,EAAQtc,UACnD/B,EAAQ2W,OAAO9V,EAAMqD,OAAQ,CAC3BqT,eAAe,KACZvX,EAAQkG,SAASsS,eAEtB6F,EAAQtc,QAAUlB,EAAMqD,OACxBjD,EAAiBqP,IAAK,IAAWA,MAEjCtQ,EAAQsY,uBAET,CAACtY,EAASa,EAAMqD,SAEnBzE,EAAMkB,UAAU,KACTX,EAAQqG,OAAOD,QAClBpG,EAAQiC,YACRjC,EAAQqG,OAAOD,OAAQ,GAGrBpG,EAAQqG,OAAO7D,QACjBxC,EAAQqG,OAAO7D,OAAQ,EACvBxC,EAAQqQ,UAAUC,MAAMhJ,KAAK,IAAKtH,EAAQmB,cAG5CnB,EAAQqE,qBAGV+Z,EAAarc,QAAQhC,UAAYD,EAAkBC,EAAWC,GAEvDoe,EAAarc,OACtB"}